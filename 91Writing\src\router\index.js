import { createRouter, createWebHashHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'
import PromptsLibrary from '../views/PromptsLibrary.vue'
import Settings from '../views/Settings.vue'
import ToolsLibrary from '../views/ToolsLibrary.vue'
import BookAnalysis from '../views/BookAnalysis.vue'

const routes = [
  {
    path: '/',
    component: Dashboard,
    children: [
      {
        path: '',
        name: 'BookAnalysis',
        component: BookAnalysis
      },
      {
        path: 'prompts',
        name: 'PromptsLibrary',
        component: PromptsLibrary
      },
      {
        path: 'settings',
        name: 'Settings',
        component: Settings
      },
      {
        path: 'tools',
        name: 'ToolsLibrary',
        component: ToolsLibrary
      },
      {
        path: 'book-analysis',
        name: 'BookAnalysisPage',
        component: BookAnalysis
      },
      // 重定向已删除的页面到拆书工具
      {
        path: 'novels',
        redirect: '/book-analysis'
      },
      {
        path: 'novel-management',
        redirect: '/book-analysis'
      },
      {
        path: 'chapters',
        redirect: '/book-analysis'
      },
      {
        path: 'goals',
        redirect: '/book-analysis'
      },
      {
        path: 'billing',
        redirect: '/book-analysis'
      },
      {
        path: 'writer',
        redirect: '/book-analysis'
      },
      {
        path: 'genres',
        redirect: '/book-analysis'
      },
      {
        path: 'short-story',
        redirect: '/book-analysis'
      },
      {
        path: 'config',
        redirect: '/settings'
      }
    ]
  }
  ]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router