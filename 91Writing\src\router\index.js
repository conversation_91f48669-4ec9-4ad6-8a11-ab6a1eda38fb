import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'
import PromptsLibrary from '../views/PromptsLibrary.vue'
import Settings from '../views/Settings.vue'
import ToolsLibrary from '../views/ToolsLibrary.vue'
import BookAnalysis from '../views/BookAnalysis.vue'

const routes = [
  {
    path: '/',
    component: Dashboard,
    children: [
      {
        path: '',
        name: 'BookAnalysis',
        component: BookAnalysis
      },
      {
        path: 'prompts',
        name: 'PromptsLibrary',
        component: PromptsLibrary
      },
      {
        path: 'settings',
        name: 'Settings',
        component: Settings
      },
      {
        path: 'tools',
        name: 'ToolsLibrary',
        component: ToolsLibrary
      },
      {
        path: 'book-analysis',
        name: 'BookAnalysisPage',
        component: BookAnalysis
      }
    ]
  }
  ]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router