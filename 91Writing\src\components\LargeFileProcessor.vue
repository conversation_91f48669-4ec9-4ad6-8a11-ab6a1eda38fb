<template>
  <div class="large-file-processor">
    <!-- 文件上传区域 -->
    <div v-if="!isProcessing && !processResult" class="upload-section">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>📚 大文件智能拆分</h3>
            <p>支持上百万字的超大文件分段处理</p>
          </div>
        </template>
        
        <el-upload
          class="upload-area"
          drag
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-exceed="handleFileExceed"
          accept=".txt,.docx"
          :limit="1"
          :show-file-list="false"
        >
          <el-icon class="el-icon--upload">
            <UploadFilled />
          </el-icon>
          <div class="el-upload__text">
            拖拽超大文件到此处或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 .txt 和 .docx 格式，文件大小无限制
              <br>
              <span class="highlight">✨ 智能分段处理，支持上百万字文件</span>
            </div>
          </template>
        </el-upload>

        <!-- 文件信息显示 -->
        <div v-if="selectedFile" class="file-info">
          <el-divider>文件信息</el-divider>
          <div class="file-details">
            <div class="detail-item">
              <span class="label">文件名：</span>
              <span class="value">{{ selectedFile.name }}</span>
            </div>
            <div class="detail-item">
              <span class="label">文件大小：</span>
              <span class="value">{{ formatFileSize(selectedFile.size) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">预估字数：</span>
              <span class="value">{{ estimatedWordCount.toLocaleString() }} 字</span>
            </div>
            <div class="detail-item">
              <span class="label">处理策略：</span>
              <span class="value strategy">{{ processingStrategy }}</span>
            </div>
          </div>
          
          <!-- 编码选择 -->
          <div class="encoding-section">
            <el-divider>编码设置</el-divider>
            <el-radio-group v-model="selectedEncoding" class="encoding-options">
              <el-radio label="utf-8">UTF-8 (推荐)</el-radio>
              <el-radio label="gbk">GBK/GB2312</el-radio>
            </el-radio-group>
          </div>

          <!-- 分段设置 -->
          <div class="segment-settings">
            <el-divider>分段设置</el-divider>
            <el-form :model="segmentConfig" label-width="120px">
              <el-form-item label="每段字数：">
                <el-input-number
                  v-model="segmentConfig.chunkSize"
                  :min="10000"
                  :max="100000"
                  :step="10000"
                  style="width: 200px"
                />
                <span class="form-tip">建议 30000-50000 字</span>
              </el-form-item>
              <el-form-item label="重叠字数：">
                <el-input-number
                  v-model="segmentConfig.overlapSize"
                  :min="1000"
                  :max="10000"
                  :step="1000"
                  style="width: 200px"
                />
                <span class="form-tip">段落间重叠，保证连贯性</span>
              </el-form-item>
            </el-form>
          </div>

          <!-- 开始处理按钮 -->
          <div class="action-buttons">
            <el-button type="primary" size="large" @click="startProcessing" :loading="isProcessing">
              <el-icon><MagicStick /></el-icon>
              开始智能拆分
            </el-button>
            <el-button @click="clearFile">
              <el-icon><Delete /></el-icon>
              重新选择
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 处理进度区域 -->
    <div v-if="isProcessing" class="processing-section">
      <ProcessingMonitor
        :is-processing="isProcessing"
        :current-step="currentStep"
        :total-steps="totalSteps"
        :current-step-text="currentStepText"
        :segments="segmentProgress"
        :logs="processingLogs"
        :errors="processingErrors"
        :stats="processingStats"
        @retry-segment="retrySegment"
        @skip-segment="skipSegment"
        @clear-logs="clearLogs"
      />

      <!-- 取消按钮 -->
      <div class="cancel-section">
        <el-button type="danger" size="large" @click="cancelProcessing">
          <el-icon><Close /></el-icon>
          取消处理
        </el-button>
      </div>
    </div>

    <!-- 处理结果区域 -->
    <div v-if="processResult && !isProcessing" class="result-section">
      <el-card shadow="hover">
        <template #header>
          <div class="result-header">
            <h3>✅ 处理完成</h3>
            <el-tag type="success" size="small">
              成功处理 {{ processResult.totalSegments }} 个分段
            </el-tag>
          </div>
        </template>

        <!-- 处理统计 -->
        <div class="result-stats">
          <div class="stat-item">
            <div class="stat-value">{{ processResult.totalChapters }}</div>
            <div class="stat-label">检测章节</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ processResult.totalWords.toLocaleString() }}</div>
            <div class="stat-label">总字数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ processResult.processingTime }}</div>
            <div class="stat-label">处理时间</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ processResult.totalSegments }}</div>
            <div class="stat-label">分段数量</div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="result-actions">
          <el-button type="primary" size="large" @click="viewResults">
            <el-icon><View /></el-icon>
            查看拆分结果
          </el-button>
          <el-button type="success" @click="exportResults">
            <el-icon><Download /></el-icon>
            导出结果
          </el-button>
          <el-button @click="startOver">
            <el-icon><Refresh /></el-icon>
            重新处理
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  UploadFilled, MagicStick, Delete, Close,
  View, Download, Refresh
} from '@element-plus/icons-vue'
import ProcessingMonitor from './ProcessingMonitor.vue'

// 响应式数据
const selectedFile = ref(null)
const selectedEncoding = ref('utf-8')
const isProcessing = ref(false)
const processResult = ref(null)
const currentStep = ref(0)
const totalSteps = ref(0)
const currentStepText = ref('')
const segmentProgress = ref([])
const processingLogs = ref([])
const processingErrors = ref([])
const startTime = ref(0)

// 分段配置
const segmentConfig = ref({
  chunkSize: 30000,  // 每段30000字
  overlapSize: 3000  // 重叠3000字
})

// 计算属性
const estimatedWordCount = computed(() => {
  if (!selectedFile.value) return 0
  // 粗略估算：1字节约等于0.5个中文字符
  return Math.floor(selectedFile.value.size * 0.5)
})

const processingStrategy = computed(() => {
  const wordCount = estimatedWordCount.value
  if (wordCount < 50000) {
    return '小文件 - 直接处理'
  } else if (wordCount < 200000) {
    return '中等文件 - 简单分段'
  } else if (wordCount < 500000) {
    return '大文件 - 智能分段'
  } else {
    return '超大文件 - 高级分段处理'
  }
})

const overallProgress = computed(() => {
  if (totalSteps.value === 0) return 0
  return (currentStep.value / totalSteps.value) * 100
})

const processingStats = computed(() => {
  const processedSegments = segmentProgress.value.filter(s => s.status === 'completed').length
  const totalSegments = segmentProgress.value.length
  const detectedChapters = segmentProgress.value.reduce((sum, s) => sum + (s.chapters || 0), 0)
  const elapsedTime = startTime.value > 0 ? Math.floor((Date.now() - startTime.value) / 1000) : 0

  // 计算处理速度
  let processingSpeed = '0 字/秒'
  if (elapsedTime > 0 && processedSegments > 0) {
    const processedWords = segmentProgress.value
      .filter(s => s.status === 'completed')
      .reduce((sum, s) => sum + s.size, 0)
    const speed = Math.floor(processedWords / elapsedTime)
    processingSpeed = `${speed.toLocaleString()} 字/秒`
  }

  return {
    processedSegments,
    totalSegments,
    detectedChapters,
    elapsedTime,
    processingSpeed
  }
})

// 方法
const handleFileChange = (file) => {
  selectedFile.value = file
  addLog('info', `选择文件: ${file.name} (${formatFileSize(file.size)})`)
}

const handleFileExceed = (files) => {
  if (files.length > 0) {
    const newFile = files[0]
    const fileObj = {
      name: newFile.name,
      size: newFile.size,
      raw: newFile,
      status: 'ready'
    }
    ElMessage.success('正在替换当前文件...')
    handleFileChange(fileObj)
  }
}

const clearFile = () => {
  selectedFile.value = null
  processResult.value = null
  processingLogs.value = []
  addLog('info', '已清除文件选择')
}

const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(1) + ' MB'
  }
}

const getStatusText = (status) => {
  const statusMap = {
    'waiting': '等待中',
    'processing': '处理中',
    'completed': '已完成',
    'error': '处理失败'
  }
  return statusMap[status] || '未知状态'
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const addLog = (type, message) => {
  processingLogs.value.push({
    type,
    message,
    timestamp: Date.now()
  })
}

// 开始处理文件
const startProcessing = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请先选择文件')
    return
  }

  isProcessing.value = true
  currentStep.value = 0
  totalSteps.value = 5 // 基础步骤数
  startTime.value = Date.now()
  processingErrors.value = []

  try {
    addLog('info', '开始处理大文件...')

    // 步骤1: 读取文件
    await updateStep(1, '正在读取文件内容...')
    const content = await readFileContent()

    // 步骤2: 分析文件
    await updateStep(2, '正在分析文件结构...')
    const analysis = analyzeContent(content)

    // 步骤3: 创建分段
    await updateStep(3, '正在创建智能分段...')
    const segments = createSegments(content, analysis)

    // 步骤4: 处理分段
    await updateStep(4, '正在处理各个分段...')
    const results = await processSegments(segments)

    // 步骤5: 合并结果
    await updateStep(5, '正在合并处理结果...')
    const finalResult = mergeResults(results)

    processResult.value = finalResult
    addLog('success', `处理完成！共检测到 ${finalResult.totalChapters} 个章节`)

  } catch (error) {
    addLog('error', `处理失败: ${error.message}`)
    ElMessage.error('文件处理失败，请重试')
  } finally {
    isProcessing.value = false
  }
}

// 更新处理步骤
const updateStep = async (step, text) => {
  currentStep.value = step
  currentStepText.value = text
  addLog('info', text)
  
  // 模拟处理时间
  await new Promise(resolve => setTimeout(resolve, 1000))
}

// 读取文件内容
const readFileContent = () => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      const content = e.target.result
      addLog('success', `文件读取完成，内容长度: ${content.length.toLocaleString()} 字符`)
      resolve(content)
    }
    
    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }
    
    // 根据编码读取文件
    if (selectedEncoding.value === 'gbk') {
      reader.readAsText(selectedFile.value.raw, 'GBK')
    } else {
      reader.readAsText(selectedFile.value.raw, 'UTF-8')
    }
  })
}

// 分析文件内容
const analyzeContent = (content) => {
  addLog('info', '正在分析文件结构和章节模式...')
  
  // 检测章节模式
  const chapterPatterns = [
    /第[一二三四五六七八九十\d]+章/g,
    /第[一二三四五六七八九十\d]+节/g,
    /Chapter\s*\d+/gi,
    /\d+\./g
  ]
  
  let detectedPattern = null
  let maxMatches = 0
  
  chapterPatterns.forEach(pattern => {
    const matches = content.match(pattern) || []
    if (matches.length > maxMatches) {
      maxMatches = matches.length
      detectedPattern = pattern
    }
  })
  
  return {
    totalLength: content.length,
    estimatedChapters: maxMatches,
    chapterPattern: detectedPattern,
    needsSegmentation: content.length > segmentConfig.value.chunkSize
  }
}

// 创建分段
const createSegments = (content, analysis) => {
  const segments = []
  const chunkSize = segmentConfig.value.chunkSize
  const overlapSize = segmentConfig.value.overlapSize
  
  if (!analysis.needsSegmentation) {
    // 小文件直接处理
    segments.push({
      index: 0,
      content: content,
      size: content.length,
      status: 'waiting'
    })
  } else {
    // 大文件分段处理
    let start = 0
    let segmentIndex = 0
    
    while (start < content.length) {
      const end = Math.min(start + chunkSize, content.length)
      const segmentContent = content.slice(start, end)
      
      segments.push({
        index: segmentIndex,
        content: segmentContent,
        size: segmentContent.length,
        status: 'waiting'
      })
      
      start = end - overlapSize
      segmentIndex++
    }
  }
  
  segmentProgress.value = segments
  addLog('info', `创建了 ${segments.length} 个处理分段`)
  
  return segments
}

// 处理分段
const processSegments = async (segments) => {
  const results = []

  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i]

    try {
      // 更新状态
      segmentProgress.value[i].status = 'processing'
      addLog('info', `正在处理第 ${i + 1} 段 (${segment.size.toLocaleString()} 字)`)

      // 使用AI进行智能章节检测
      const chapters = await processSegmentWithAI(segment.content, i)

      results.push({
        segmentIndex: i,
        chapters: chapters,
        wordCount: segment.size
      })

      segmentProgress.value[i].status = 'completed'
      addLog('success', `第 ${i + 1} 段处理完成，检测到 ${chapters.length} 个章节`)

    } catch (error) {
      segmentProgress.value[i].status = 'error'
      addLog('error', `第 ${i + 1} 段处理失败: ${error.message}`)

      // 记录错误
      processingErrors.value.push({
        segmentIndex: i,
        message: error.message,
        timestamp: Date.now()
      })

      // 降级到本地处理
      const fallbackChapters = detectChaptersInSegment(segment.content)
      results.push({
        segmentIndex: i,
        chapters: fallbackChapters,
        wordCount: segment.size,
        error: error.message,
        fallback: true
      })
    }
  }

  return results
}

// 使用AI处理分段
const processSegmentWithAI = async (content, segmentIndex) => {
  try {
    // 检查是否配置了API
    const apiConfig = localStorage.getItem('apiConfig')
    if (!apiConfig) {
      throw new Error('未配置AI API，使用本地处理')
    }

    const config = JSON.parse(apiConfig)
    if (!config.apiKey || !config.baseUrl) {
      throw new Error('API配置不完整，使用本地处理')
    }

    // 构建AI提示词
    const prompt = `请分析以下文本内容，智能识别其中的章节结构。

文本内容：
${content}

请按照以下要求进行分析：
1. 识别明显的章节标题（如"第X章"、"Chapter X"等）
2. 如果没有明显标题，根据内容逻辑划分合理的章节
3. 每个章节应该有完整的情节或主题
4. 为每个章节生成简洁的标题

请以JSON格式返回结果：
{
  "chapters": [
    {
      "title": "章节标题",
      "startIndex": 开始位置,
      "endIndex": 结束位置,
      "summary": "章节简介"
    }
  ]
}`

    addLog('info', `正在使用AI分析第 ${segmentIndex + 1} 段内容...`)

    // 调用AI API
    const response = await fetch(`${config.baseUrl}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: config.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.3
      })
    })

    if (!response.ok) {
      throw new Error(`AI API请求失败: ${response.status}`)
    }

    const data = await response.json()
    const aiResult = data.choices[0].message.content

    // 解析AI返回的JSON
    try {
      const parsed = JSON.parse(aiResult)
      if (parsed.chapters && Array.isArray(parsed.chapters)) {
        return parsed.chapters.map((chapter, index) => ({
          index: index,
          title: chapter.title || `第${index + 1}章`,
          startIndex: chapter.startIndex || 0,
          endIndex: chapter.endIndex || content.length,
          summary: chapter.summary || '',
          content: content.slice(chapter.startIndex || 0, chapter.endIndex || content.length),
          aiGenerated: true
        }))
      }
    } catch (parseError) {
      addLog('warning', `AI返回结果解析失败，使用本地处理: ${parseError.message}`)
    }

    // 如果AI解析失败，降级到本地处理
    return detectChaptersInSegment(content)

  } catch (error) {
    addLog('warning', `AI处理失败，降级到本地处理: ${error.message}`)
    return detectChaptersInSegment(content)
  }
}

// 在分段中检测章节
const detectChaptersInSegment = (content) => {
  const chapters = []

  // 多种章节模式检测
  const chapterPatterns = [
    /第[一二三四五六七八九十百千万\d]+[章节][^\n]*/g,
    /Chapter\s*\d+[^\n]*/gi,
    /第\d+章[^\n]*/g,
    /\d+\.\s*[^\n]{1,50}/g, // 数字标题
    /[一二三四五六七八九十]+、[^\n]{1,50}/g // 中文数字标题
  ]

  let bestPattern = null
  let maxMatches = 0

  // 找到匹配最多的模式
  for (const pattern of chapterPatterns) {
    const matches = [...content.matchAll(pattern)]
    if (matches.length > maxMatches) {
      maxMatches = matches.length
      bestPattern = pattern
    }
  }

  if (bestPattern && maxMatches > 0) {
    // 使用最佳模式提取章节
    const matches = [...content.matchAll(bestPattern)]

    for (let i = 0; i < matches.length; i++) {
      const match = matches[i]
      const nextMatch = matches[i + 1]

      const startIndex = match.index
      const endIndex = nextMatch ? nextMatch.index : content.length
      const chapterContent = content.slice(startIndex, endIndex).trim()

      chapters.push({
        index: i,
        title: match[0].trim(),
        startIndex: startIndex,
        endIndex: endIndex,
        content: chapterContent,
        wordCount: chapterContent.length,
        summary: generateQuickSummary(chapterContent)
      })
    }
  } else {
    // 如果没有明显的章节标记，按长度智能分割
    const avgChapterLength = Math.max(3000, Math.floor(content.length / 10)) // 至少3000字一章
    let currentPos = 0
    let chapterIndex = 0

    while (currentPos < content.length) {
      const endPos = Math.min(currentPos + avgChapterLength, content.length)

      // 寻找合适的断点
      let breakPoint = endPos
      for (let i = endPos; i < Math.min(endPos + 500, content.length); i++) {
        if (content[i] === '。' || content[i] === '\n\n' || content[i] === '！' || content[i] === '？') {
          breakPoint = i + 1
          break
        }
      }

      const chapterContent = content.slice(currentPos, breakPoint).trim()
      if (chapterContent.length > 100) { // 至少100字才算一章
        chapters.push({
          index: chapterIndex,
          title: `第${chapterIndex + 1}章`,
          startIndex: currentPos,
          endIndex: breakPoint,
          content: chapterContent,
          wordCount: chapterContent.length,
          summary: generateQuickSummary(chapterContent),
          autoGenerated: true
        })
        chapterIndex++
      }

      currentPos = breakPoint
    }
  }

  return chapters
}

// 生成快速摘要
const generateQuickSummary = (content) => {
  if (!content || content.length < 50) return '内容较短'

  // 提取前100字作为摘要
  const summary = content.slice(0, 100).replace(/\n/g, ' ').trim()
  return summary + (content.length > 100 ? '...' : '')
}

// 合并结果
const mergeResults = (results) => {
  const startTime = Date.now()
  let totalWords = 0
  const allChapters = []
  let globalChapterIndex = 0

  // 智能合并章节，处理重叠内容
  for (let i = 0; i < results.length; i++) {
    const result = results[i]
    totalWords += result.wordCount

    if (result.chapters && result.chapters.length > 0) {
      for (let j = 0; j < result.chapters.length; j++) {
        const chapter = result.chapters[j]

        // 检查是否与前一个章节重叠
        let shouldAdd = true
        if (allChapters.length > 0) {
          const lastChapter = allChapters[allChapters.length - 1]

          // 如果标题相似或内容重叠，跳过
          if (isSimilarChapter(lastChapter, chapter)) {
            shouldAdd = false
            addLog('info', `跳过重复章节: ${chapter.title}`)
          }
        }

        if (shouldAdd) {
          allChapters.push({
            ...chapter,
            globalIndex: globalChapterIndex,
            segmentIndex: i,
            originalIndex: chapter.index
          })
          globalChapterIndex++
        }
      }
    }
  }

  // 后处理：清理和优化章节
  const cleanedChapters = postProcessChapters(allChapters)

  const processingTime = Math.round((Date.now() - startTime) / 1000)

  return {
    totalChapters: cleanedChapters.length,
    totalWords,
    totalSegments: results.length,
    processingTime: `${processingTime} 秒`,
    chapters: cleanedChapters,
    timestamp: new Date().toLocaleString(),
    hasAiProcessing: results.some(r => r.chapters.some(c => c.aiGenerated)),
    hasFallback: results.some(r => r.fallback)
  }
}

// 检查章节是否相似
const isSimilarChapter = (chapter1, chapter2) => {
  // 标题相似度检查
  if (chapter1.title && chapter2.title) {
    const title1 = chapter1.title.toLowerCase().trim()
    const title2 = chapter2.title.toLowerCase().trim()

    if (title1 === title2) return true

    // 检查是否都是"第X章"格式
    const chapterNumPattern = /第(\d+)章/
    const match1 = title1.match(chapterNumPattern)
    const match2 = title2.match(chapterNumPattern)

    if (match1 && match2 && match1[1] === match2[1]) {
      return true
    }
  }

  // 内容重叠检查
  if (chapter1.content && chapter2.content) {
    const content1 = chapter1.content.slice(0, 200)
    const content2 = chapter2.content.slice(0, 200)

    // 如果前200字有80%以上相似，认为是重复
    const similarity = calculateSimilarity(content1, content2)
    if (similarity > 0.8) return true
  }

  return false
}

// 计算文本相似度
const calculateSimilarity = (text1, text2) => {
  if (!text1 || !text2) return 0

  const len1 = text1.length
  const len2 = text2.length
  const maxLen = Math.max(len1, len2)

  if (maxLen === 0) return 1

  // 简单的字符匹配相似度
  let matches = 0
  const minLen = Math.min(len1, len2)

  for (let i = 0; i < minLen; i++) {
    if (text1[i] === text2[i]) {
      matches++
    }
  }

  return matches / maxLen
}

// 后处理章节
const postProcessChapters = (chapters) => {
  return chapters.map((chapter, index) => {
    // 重新编号
    const newTitle = chapter.autoGenerated
      ? `第${index + 1}章`
      : chapter.title

    return {
      ...chapter,
      index: index,
      title: newTitle,
      wordCount: chapter.content ? chapter.content.length : chapter.wordCount || 0
    }
  })
}

// 取消处理
const cancelProcessing = async () => {
  try {
    await ElMessageBox.confirm('确定要取消当前处理吗？', '确认取消', {
      type: 'warning'
    })
    
    isProcessing.value = false
    addLog('warning', '用户取消了处理')
    ElMessage.info('已取消处理')
    
  } catch (error) {
    // 用户取消确认
  }
}

// 查看结果
const viewResults = () => {
  // 触发事件，让父组件处理
  emit('view-results', processResult.value)
}

// 导出结果
const exportResults = () => {
  // 触发事件，让父组件处理
  emit('export-results', processResult.value)
}

// 重新开始
const startOver = () => {
  selectedFile.value = null
  processResult.value = null
  isProcessing.value = false
  segmentProgress.value = []
  processingLogs.value = []
  currentStep.value = 0
  addLog('info', '重新开始处理')
}

// 定义事件
const emit = defineEmits(['view-results', 'export-results'])

// 重试分段处理
const retrySegment = async (segmentIndex) => {
  if (segmentIndex >= 0 && segmentIndex < segmentProgress.value.length) {
    const segment = segmentProgress.value[segmentIndex]

    try {
      segment.status = 'processing'
      addLog('info', `重试处理第 ${segmentIndex + 1} 段...`)

      // 重新处理该分段
      const chapters = await processSegmentWithAI(segment.content, segmentIndex)

      // 更新分段信息
      segment.status = 'completed'
      segment.chapters = chapters.length

      // 移除错误记录
      processingErrors.value = processingErrors.value.filter(e => e.segmentIndex !== segmentIndex)

      addLog('success', `第 ${segmentIndex + 1} 段重试成功`)
      ElMessage.success(`第 ${segmentIndex + 1} 段重试成功`)

    } catch (error) {
      segment.status = 'error'
      addLog('error', `第 ${segmentIndex + 1} 段重试失败: ${error.message}`)
      ElMessage.error(`第 ${segmentIndex + 1} 段重试失败`)
    }
  }
}

// 跳过分段处理
const skipSegment = (segmentIndex) => {
  if (segmentIndex >= 0 && segmentIndex < segmentProgress.value.length) {
    const segment = segmentProgress.value[segmentIndex]
    segment.status = 'completed'
    segment.chapters = 0

    // 移除错误记录
    processingErrors.value = processingErrors.value.filter(e => e.segmentIndex !== segmentIndex)

    addLog('info', `已跳过第 ${segmentIndex + 1} 段`)
    ElMessage.info(`已跳过第 ${segmentIndex + 1} 段`)
  }
}

// 清空日志
const clearLogs = () => {
  processingLogs.value = []
  addLog('info', '日志已清空')
}

// 生命周期
onMounted(() => {
  addLog('info', '大文件处理器已就绪')
})
</script>

<style scoped>
.large-file-processor {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.card-header {
  text-align: center;
}

.card-header h3 {
  margin: 0 0 8px 0;
  color: #409eff;
  font-size: 24px;
}

.card-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.upload-area {
  margin: 20px 0;
}

.upload-area .el-upload-dragger {
  width: 100%;
  height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s;
}

.upload-area .el-upload-dragger:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.el-upload__tip .highlight {
  color: #409eff;
  font-weight: bold;
}

.file-info {
  margin-top: 20px;
}

.file-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin: 16px 0;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item .label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.detail-item .value {
  color: #303133;
}

.detail-item .value.strategy {
  color: #409eff;
  font-weight: bold;
}

.encoding-options {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.segment-settings {
  margin: 20px 0;
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-header h3 {
  margin: 0;
  color: #409eff;
}

.overall-progress {
  margin: 20px 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.progress-percent {
  font-weight: bold;
  color: #409eff;
}

.current-step-text {
  text-align: center;
  margin-top: 8px;
  color: #909399;
  font-size: 14px;
}

.segment-list {
  max-height: 300px;
  overflow-y: auto;
}

.segment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin: 8px 0;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
}

.segment-item.completed {
  border-color: #67c23a;
  background: #f0f9ff;
}

.segment-item.processing {
  border-color: #409eff;
  background: #ecf5ff;
}

.segment-item.error {
  border-color: #f56c6c;
  background: #fef0f0;
}

.segment-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.segment-title {
  font-weight: bold;
  color: #303133;
}

.segment-size {
  font-size: 12px;
  color: #909399;
}

.segment-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 16px;
}

.status-icon.completed {
  color: #67c23a;
}

.status-icon.processing {
  color: #409eff;
}

.status-icon.error {
  color: #f56c6c;
}

.status-icon.waiting {
  color: #909399;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.status-text {
  font-size: 12px;
  color: #606266;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: #f5f7fa;
  border-radius: 6px;
  padding: 12px;
}

.log-item {
  display: flex;
  gap: 12px;
  margin: 4px 0;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #909399;
  min-width: 80px;
}

.log-message {
  color: #606266;
}

.log-item.success .log-message {
  color: #67c23a;
}

.log-item.error .log-message {
  color: #f56c6c;
}

.log-item.warning .log-message {
  color: #e6a23c;
}

.cancel-section {
  text-align: center;
  margin-top: 20px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-header h3 {
  margin: 0;
  color: #67c23a;
}

.result-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .large-file-processor {
    padding: 10px;
  }

  .file-details {
    grid-template-columns: 1fr;
  }

  .result-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-buttons,
  .result-actions {
    flex-direction: column;
  }
}
</style>
