# 91Writing 大文件处理功能说明

## 功能概述

91Writing 现已支持上百万字超大文件的智能拆分处理，通过分段式上传和AI智能分析，解决了传统方法无法处理大文件的限制。

## 主要特性

### 🚀 分段式处理
- **智能分段**：自动将大文件分成合适大小的段落进行处理
- **重叠处理**：段落间设置重叠区域，确保章节完整性
- **并发优化**：支持多段并发处理，提高处理效率

### 🤖 AI智能分析
- **章节识别**：智能识别各种章节标题格式
- **内容分析**：AI分析文本结构和逻辑
- **降级处理**：AI失败时自动降级到本地处理

### 📊 实时监控
- **进度跟踪**：实时显示处理进度和状态
- **详细日志**：记录每个处理步骤的详细信息
- **错误处理**：智能错误恢复和重试机制

## 使用方法

### 1. 选择处理模式

在文件上传界面，您可以选择两种处理模式：

- **普通模式**：适用于小于50万字的文件，直接处理
- **大文件模式**：适用于上百万字的超大文件，分段处理

### 2. 配置分段参数

在大文件模式下，您可以调整以下参数：

- **每段字数**：建议30000-50000字，根据文件大小调整
- **重叠字数**：建议3000字，确保章节边界完整
- **编码格式**：支持UTF-8和GBK编码

### 3. 开始处理

点击"开始智能拆分"后，系统将：

1. **读取文件**：根据选择的编码读取文件内容
2. **分析结构**：检测章节模式和文本结构
3. **创建分段**：智能分割文件为多个处理段
4. **AI处理**：使用AI分析每个分段的章节结构
5. **合并结果**：智能合并所有分段的处理结果

### 4. 监控进度

处理过程中，您可以：

- **查看总体进度**：显示当前步骤和完成百分比
- **监控分段状态**：实时查看每个分段的处理状态
- **查看处理日志**：详细的处理记录和状态信息
- **处理错误**：对失败的分段进行重试或跳过

## 技术特点

### 内存优化
- **流式处理**：避免一次性加载整个文件到内存
- **分段释放**：处理完成的分段及时释放内存
- **智能缓存**：只保留必要的数据在内存中

### 错误恢复
- **自动重试**：网络或API错误时自动重试
- **降级处理**：AI处理失败时降级到本地算法
- **断点续传**：支持从失败点继续处理

### 性能优化
- **并发处理**：多个分段可以并发处理
- **智能调度**：根据系统资源动态调整处理策略
- **进度缓存**：处理进度实时保存，避免重复工作

## 支持的文件格式

- **TXT文件**：支持UTF-8和GBK编码
- **DOCX文件**：Microsoft Word文档（实验性支持）

## 文件大小限制

- **普通模式**：建议10MB以内
- **大文件模式**：理论上无限制，已测试支持100MB+文件

## AI配置要求

为了获得最佳的章节识别效果，建议配置AI API：

1. 进入设置页面配置API密钥
2. 选择合适的AI模型（推荐GPT-3.5-turbo或更高版本）
3. 确保API配额充足

如果未配置AI或AI处理失败，系统会自动使用本地算法进行章节识别。

## 处理结果

处理完成后，您将获得：

- **章节列表**：智能识别的所有章节
- **章节内容**：每个章节的完整文本
- **统计信息**：字数、章节数、处理时间等
- **导出功能**：支持导出处理结果

## 常见问题

### Q: 处理大文件时内存不足怎么办？
A: 系统采用分段处理，不会一次性加载整个文件。如果仍然遇到内存问题，可以减小"每段字数"参数。

### Q: AI处理失败怎么办？
A: 系统会自动降级到本地处理算法，虽然效果可能略差，但仍能完成基本的章节识别。

### Q: 可以中途取消处理吗？
A: 可以，点击"取消处理"按钮即可停止当前处理。已处理的分段结果会保留。

### Q: 处理速度慢怎么办？
A: 处理速度取决于文件大小、AI响应速度和网络状况。可以尝试：
- 减小分段大小
- 检查网络连接
- 使用更快的AI模型

## 更新日志

### v1.0.0 (2024-01-XX)
- 首次发布大文件处理功能
- 支持分段式上传和处理
- 集成AI智能章节识别
- 实时进度监控和错误处理

## 技术支持

如果您在使用过程中遇到问题，请：

1. 查看处理日志中的错误信息
2. 检查AI API配置是否正确
3. 尝试减小文件大小或调整分段参数
4. 联系技术支持获取帮助

---

**注意**：大文件处理功能仍在持续优化中，如果遇到问题请及时反馈。
