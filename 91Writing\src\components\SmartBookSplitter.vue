<template>
  <div class="smart-book-splitter">
    <!-- 文件上传区域 -->
    <div v-if="!isProcessing && !splitResult" class="upload-section">
      <el-card shadow="hover" class="upload-card">
        <template #header>
          <div class="card-header">
            <h3>📖 智能一键拆书</h3>
            <p>自动识别章节，智能拆分整本小说</p>
          </div>
        </template>
        
        <!-- 处理模式选择 -->
        <div class="mode-selection">
          <el-radio-group v-model="processingMode" size="large">
            <el-radio-button label="auto">智能模式</el-radio-button>
            <el-radio-button label="manual">手动模式</el-radio-button>
            <el-radio-button label="large">大文件模式</el-radio-button>
          </el-radio-group>
          <div class="mode-description">
            <span v-if="processingMode === 'auto'">🤖 AI自动识别章节格式，适合大部分小说</span>
            <span v-else-if="processingMode === 'manual'">🔧 手动配置章节规则，精确控制拆分</span>
            <span v-else>🚀 专为超大文件设计，支持上百万字小说</span>
          </div>
        </div>

        <!-- 文件上传 -->
        <el-upload
          class="upload-area"
          drag
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-exceed="handleFileExceed"
          accept=".txt,.docx"
          :limit="1"
          :show-file-list="false"
        >
          <el-icon class="el-icon--upload">
            <UploadFilled />
          </el-icon>
          <div class="el-upload__text">
            拖拽小说文件到此处或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 .txt 和 .docx 格式
              <br>
              <span v-if="processingMode === 'large'" class="highlight">
                ✨ 大文件模式：支持无限大小文件
              </span>
              <span v-else class="size-tip">
                建议文件大小小于50MB
              </span>
            </div>
          </template>
        </el-upload>

        <!-- 文件信息 -->
        <div v-if="selectedFile" class="file-info">
          <el-divider>文件信息</el-divider>
          <div class="file-details">
            <div class="detail-row">
              <span class="label">文件名：</span>
              <span class="value">{{ selectedFile.name }}</span>
            </div>
            <div class="detail-row">
              <span class="label">文件大小：</span>
              <span class="value">{{ formatFileSize(selectedFile.size) }}</span>
            </div>
            <div class="detail-row">
              <span class="label">预估字数：</span>
              <span class="value">{{ estimatedWordCount.toLocaleString() }} 字</span>
            </div>
            <div class="detail-row">
              <span class="label">处理策略：</span>
              <span class="value strategy">{{ processingStrategy }}</span>
            </div>
          </div>

          <!-- 编码选择 -->
          <div class="encoding-section">
            <el-divider>编码设置</el-divider>
            <el-radio-group v-model="selectedEncoding" class="encoding-options">
              <el-radio label="utf-8">UTF-8 (推荐)</el-radio>
              <el-radio label="gbk">GBK/GB2312</el-radio>
            </el-radio-group>
          </div>

          <!-- 拆分配置 -->
          <div class="split-config" v-if="processingMode !== 'auto'">
            <el-divider>拆分配置</el-divider>
            
            <!-- 手动模式配置 -->
            <div v-if="processingMode === 'manual'" class="manual-config">
              <el-form :model="manualConfig" label-width="120px">
                <el-form-item label="章节模式：">
                  <el-select v-model="manualConfig.chapterPattern" placeholder="选择章节模式">
                    <el-option 
                      v-for="pattern in chapterPatterns" 
                      :key="pattern.value"
                      :label="pattern.label" 
                      :value="pattern.value"
                    >
                      <span>{{ pattern.label }}</span>
                      <span style="float: right; color: #8492a6; font-size: 12px">{{ pattern.example }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="自定义规则：" v-if="manualConfig.chapterPattern === 'custom'">
                  <el-input 
                    v-model="manualConfig.customPattern" 
                    placeholder="输入正则表达式"
                    clearable
                  />
                  <div class="form-tip">例如：第[\\d一二三四五六七八九十]+章</div>
                </el-form-item>
                <el-form-item label="最小章节长度：">
                  <el-input-number
                    v-model="manualConfig.minChapterLength"
                    :min="100"
                    :max="10000"
                    :step="100"
                    style="width: 200px"
                  />
                  <span class="form-tip">字符数</span>
                </el-form-item>
              </el-form>
            </div>

            <!-- 大文件模式配置 -->
            <div v-else-if="processingMode === 'large'" class="large-config">
              <el-form :model="largeConfig" label-width="120px">
                <el-form-item label="分段大小：">
                  <el-input-number
                    v-model="largeConfig.chunkSize"
                    :min="10000"
                    :max="100000"
                    :step="10000"
                    style="width: 200px"
                  />
                  <span class="form-tip">每段字符数</span>
                </el-form-item>
                <el-form-item label="重叠大小：">
                  <el-input-number
                    v-model="largeConfig.overlapSize"
                    :min="1000"
                    :max="10000"
                    :step="1000"
                    style="width: 200px"
                  />
                  <span class="form-tip">段落重叠字符数</span>
                </el-form-item>
                <el-form-item label="AI处理：">
                  <el-switch
                    v-model="largeConfig.useAI"
                    active-text="启用AI智能识别"
                    inactive-text="仅使用本地算法"
                  />
                </el-form-item>
              </el-form>
            </div>
          </div>

          <!-- 开始拆书按钮 -->
          <div class="action-buttons">
            <el-button 
              type="primary" 
              size="large" 
              @click="startSplitting" 
              :loading="isProcessing"
              block
            >
              <el-icon><MagicStick /></el-icon>
              {{ getButtonText() }}
            </el-button>
            <el-button @click="clearFile" block>
              <el-icon><Delete /></el-icon>
              重新选择文件
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 处理进度区域 -->
    <div v-if="isProcessing" class="processing-section">
      <el-card shadow="hover">
        <template #header>
          <div class="progress-header">
            <h3>🔄 正在智能拆书...</h3>
            <el-tag :type="getProgressTagType()" size="small">
              {{ getProgressText() }}
            </el-tag>
          </div>
        </template>

        <!-- 总体进度 -->
        <div class="overall-progress">
          <div class="progress-info">
            <span>拆书进度：{{ currentStep }}/{{ totalSteps }}</span>
            <span class="progress-percent">{{ Math.round(overallProgress) }}%</span>
          </div>
          <el-progress
            :percentage="overallProgress"
            :stroke-width="12"
            :show-text="false"
            :status="progressStatus"
          />
          <div class="current-step-text">{{ currentStepText }}</div>
        </div>

        <!-- 实时统计 -->
        <div class="real-time-stats">
          <div class="stat-item">
            <div class="stat-value">{{ detectedChapters }}</div>
            <div class="stat-label">已识别章节</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ processedWords.toLocaleString() }}</div>
            <div class="stat-label">已处理字数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatTime(elapsedTime) }}</div>
            <div class="stat-label">已用时间</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ processingSpeed }}</div>
            <div class="stat-label">处理速度</div>
          </div>
        </div>

        <!-- 处理日志 -->
        <div class="processing-logs" v-if="processingLogs.length > 0">
          <el-divider>处理日志</el-divider>
          <div class="log-container">
            <div
              v-for="(log, index) in recentLogs"
              :key="index"
              class="log-item"
              :class="log.type"
            >
              <span class="log-time">{{ formatLogTime(log.timestamp) }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>

        <!-- 取消按钮 -->
        <div class="cancel-section">
          <el-button type="danger" @click="cancelProcessing">
            <el-icon><Close /></el-icon>
            取消拆书
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 拆书结果区域 -->
    <div v-if="splitResult && !isProcessing" class="result-section">
      <el-card shadow="hover">
        <template #header>
          <div class="result-header">
            <h3>✅ 拆书完成</h3>
            <el-tag type="success" size="small">
              成功识别 {{ splitResult.chapters.length }} 个章节
            </el-tag>
          </div>
        </template>

        <!-- 拆书统计 -->
        <div class="result-stats">
          <div class="stat-card">
            <div class="stat-icon">📚</div>
            <div class="stat-content">
              <div class="stat-number">{{ splitResult.chapters.length }}</div>
              <div class="stat-text">章节总数</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📝</div>
            <div class="stat-content">
              <div class="stat-number">{{ splitResult.totalWords.toLocaleString() }}</div>
              <div class="stat-text">总字数</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">⏱️</div>
            <div class="stat-content">
              <div class="stat-number">{{ splitResult.processingTime }}</div>
              <div class="stat-text">处理时间</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">🎯</div>
            <div class="stat-content">
              <div class="stat-number">{{ splitResult.accuracy }}%</div>
              <div class="stat-text">识别准确率</div>
            </div>
          </div>
        </div>

        <!-- 章节预览 -->
        <div class="chapter-preview">
          <el-divider>章节预览</el-divider>
          <div class="preview-controls">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索章节..."
              clearable
              style="width: 300px"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button-group>
              <el-button 
                :type="viewMode === 'list' ? 'primary' : ''" 
                @click="viewMode = 'list'"
              >
                <el-icon><List /></el-icon>
                列表
              </el-button>
              <el-button 
                :type="viewMode === 'grid' ? 'primary' : ''" 
                @click="viewMode = 'grid'"
              >
                <el-icon><Grid /></el-icon>
                网格
              </el-button>
            </el-button-group>
          </div>

          <!-- 章节列表 -->
          <div class="chapter-list" :class="viewMode">
            <div
              v-for="(chapter, index) in filteredChapters"
              :key="index"
              class="chapter-item"
              @click="previewChapter(chapter)"
            >
              <div class="chapter-header">
                <span class="chapter-number">{{ chapter.index + 1 }}</span>
                <span class="chapter-title">{{ chapter.title }}</span>
                <span class="chapter-words">{{ chapter.wordCount.toLocaleString() }}字</span>
              </div>
              <div class="chapter-summary" v-if="chapter.summary">
                {{ chapter.summary }}
              </div>
              <div class="chapter-actions">
                <el-button size="small" @click.stop="editChapter(chapter)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button size="small" @click.stop="previewChapter(chapter)">
                  <el-icon><View /></el-icon>
                  预览
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="result-actions">
          <el-button type="primary" size="large" @click="exportChapters">
            <el-icon><Download /></el-icon>
            导出章节
          </el-button>
          <el-button type="success" @click="saveToLibrary">
            <el-icon><FolderAdd /></el-icon>
            保存到小说库
          </el-button>
          <el-button @click="startOver">
            <el-icon><Refresh /></el-icon>
            重新拆书
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 章节预览对话框 -->
    <el-dialog
      v-model="showChapterPreview"
      :title="previewChapterData?.title || '章节预览'"
      width="80%"
      top="5vh"
    >
      <div class="chapter-preview-content" v-if="previewChapterData">
        <div class="preview-header">
          <h4>{{ previewChapterData.title }}</h4>
          <div class="preview-meta">
            <span>字数：{{ previewChapterData.wordCount.toLocaleString() }}</span>
            <span>位置：第{{ previewChapterData.index + 1 }}章</span>
          </div>
        </div>
        <div class="preview-text">
          {{ previewChapterData.content }}
        </div>
      </div>
      <template #footer>
        <el-button @click="showChapterPreview = false">关闭</el-button>
        <el-button type="primary" @click="editChapter(previewChapterData)">编辑章节</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  UploadFilled, MagicStick, Delete, Close, Search, List, Grid,
  Edit, View, Download, FolderAdd, Refresh
} from '@element-plus/icons-vue'

// 响应式数据
const selectedFile = ref(null)
const selectedEncoding = ref('utf-8')
const processingMode = ref('auto')
const isProcessing = ref(false)
const splitResult = ref(null)
const currentStep = ref(0)
const totalSteps = ref(0)
const currentStepText = ref('')
const detectedChapters = ref(0)
const processedWords = ref(0)
const elapsedTime = ref(0)
const processingSpeed = ref('0 字/秒')
const processingLogs = ref([])
const startTime = ref(0)
const searchKeyword = ref('')
const viewMode = ref('list')
const showChapterPreview = ref(false)
const previewChapterData = ref(null)

// 配置数据
const manualConfig = ref({
  chapterPattern: 'auto',
  customPattern: '',
  minChapterLength: 500
})

const largeConfig = ref({
  chunkSize: 30000,
  overlapSize: 3000,
  useAI: true
})

// 章节模式选项
const chapterPatterns = [
  { value: 'auto', label: '自动识别', example: '智能检测' },
  { value: 'chapter_num', label: '第X章', example: '第一章、第1章' },
  { value: 'chapter_title', label: '章节标题', example: '第一章 开始' },
  { value: 'number_dot', label: '数字序号', example: '1. 2. 3.' },
  { value: 'custom', label: '自定义规则', example: '正则表达式' }
]

// 计算属性
const estimatedWordCount = computed(() => {
  if (!selectedFile.value) return 0
  return Math.floor(selectedFile.value.size * 0.5)
})

const processingStrategy = computed(() => {
  const wordCount = estimatedWordCount.value
  if (processingMode.value === 'large') {
    return '大文件分段处理'
  } else if (processingMode.value === 'manual') {
    return '手动规则拆分'
  } else if (wordCount > 500000) {
    return '建议使用大文件模式'
  } else {
    return 'AI智能识别'
  }
})

const overallProgress = computed(() => {
  if (totalSteps.value === 0) return 0
  return (currentStep.value / totalSteps.value) * 100
})

const progressStatus = computed(() => {
  if (currentStep.value === totalSteps.value && totalSteps.value > 0) {
    return 'success'
  }
  return undefined
})

const recentLogs = computed(() => {
  return processingLogs.value.slice(-5)
})

const filteredChapters = computed(() => {
  if (!splitResult.value || !searchKeyword.value) {
    return splitResult.value?.chapters || []
  }
  
  return splitResult.value.chapters.filter(chapter =>
    chapter.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 方法
const handleFileChange = (file) => {
  selectedFile.value = file
  addLog('info', `选择文件: ${file.name} (${formatFileSize(file.size)})`)
}

const handleFileExceed = (files) => {
  if (files.length > 0) {
    const newFile = files[0]
    const fileObj = {
      name: newFile.name,
      size: newFile.size,
      raw: newFile,
      status: 'ready'
    }
    ElMessage.success('正在替换当前文件...')
    handleFileChange(fileObj)
  }
}

const clearFile = () => {
  selectedFile.value = null
  splitResult.value = null
  processingLogs.value = []
  resetProgress()
  addLog('info', '已清除文件选择')
}

const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(1) + ' MB'
  }
}

const formatTime = (seconds) => {
  if (seconds < 60) return `${seconds}秒`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}

const formatLogTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const addLog = (type, message) => {
  processingLogs.value.push({
    type,
    message,
    timestamp: Date.now()
  })
}

const resetProgress = () => {
  currentStep.value = 0
  totalSteps.value = 0
  currentStepText.value = ''
  detectedChapters.value = 0
  processedWords.value = 0
  elapsedTime.value = 0
  processingSpeed.value = '0 字/秒'
  startTime.value = 0
}

const getButtonText = () => {
  if (processingMode.value === 'auto') {
    return '开始智能拆书'
  } else if (processingMode.value === 'manual') {
    return '开始手动拆书'
  } else {
    return '开始大文件拆书'
  }
}

const getProgressTagType = () => {
  if (processingMode.value === 'auto') return 'primary'
  if (processingMode.value === 'manual') return 'warning'
  return 'success'
}

const getProgressText = () => {
  if (processingMode.value === 'auto') return 'AI智能处理'
  if (processingMode.value === 'manual') return '手动规则处理'
  return '大文件分段处理'
}

// 定义事件
const emit = defineEmits(['split-complete', 'chapter-selected'])

// 开始拆书处理
const startSplitting = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请先选择文件')
    return
  }

  isProcessing.value = true
  resetProgress()
  startTime.value = Date.now()

  try {
    addLog('info', '开始智能拆书处理...')

    if (processingMode.value === 'large') {
      await processLargeFile()
    } else {
      await processNormalFile()
    }

    ElMessage.success('拆书完成！')
    emit('split-complete', splitResult.value)

  } catch (error) {
    addLog('error', `拆书失败: ${error.message}`)
    ElMessage.error('拆书处理失败，请重试')
  } finally {
    isProcessing.value = false
  }
}

// 处理普通文件
const processNormalFile = async () => {
  totalSteps.value = 4

  // 步骤1: 读取文件
  await updateStep(1, '正在读取文件内容...')
  const content = await readFileContent()

  // 步骤2: 分析文件结构
  await updateStep(2, '正在分析文件结构...')
  const analysis = analyzeFileStructure(content)

  // 步骤3: 识别章节
  await updateStep(3, '正在识别章节...')
  const chapters = await identifyChapters(content, analysis)

  // 步骤4: 生成结果
  await updateStep(4, '正在生成拆书结果...')
  splitResult.value = generateSplitResult(chapters, content)
}

// 处理大文件
const processLargeFile = async () => {
  totalSteps.value = 6

  // 步骤1: 读取文件
  await updateStep(1, '正在读取大文件...')
  const content = await readFileContent()

  // 步骤2: 分析文件
  await updateStep(2, '正在分析文件结构...')
  const analysis = analyzeFileStructure(content)

  // 步骤3: 创建分段
  await updateStep(3, '正在创建智能分段...')
  const segments = createFileSegments(content, analysis)

  // 步骤4: 处理分段
  await updateStep(4, '正在处理各个分段...')
  const segmentResults = await processFileSegments(segments)

  // 步骤5: 合并章节
  await updateStep(5, '正在合并章节结果...')
  const chapters = mergeSegmentChapters(segmentResults)

  // 步骤6: 生成结果
  await updateStep(6, '正在生成最终结果...')
  splitResult.value = generateSplitResult(chapters, content)
}

// 更新处理步骤
const updateStep = async (step, text) => {
  currentStep.value = step
  currentStepText.value = text
  addLog('info', text)

  // 更新已用时间
  if (startTime.value > 0) {
    elapsedTime.value = Math.floor((Date.now() - startTime.value) / 1000)
  }

  // 模拟处理时间
  await new Promise(resolve => setTimeout(resolve, 500))
}

// 读取文件内容
const readFileContent = () => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      const content = e.target.result
      processedWords.value = content.length
      addLog('success', `文件读取完成，内容长度: ${content.length.toLocaleString()} 字符`)
      resolve(content)
    }

    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }

    // 根据编码读取文件
    if (selectedEncoding.value === 'gbk') {
      reader.readAsText(selectedFile.value.raw, 'GBK')
    } else {
      reader.readAsText(selectedFile.value.raw, 'UTF-8')
    }
  })
}

// 分析文件结构
const analyzeFileStructure = (content) => {
  addLog('info', '正在分析文件结构和章节模式...')

  // 检测各种章节模式
  const patterns = [
    { name: '第X章', regex: /第[一二三四五六七八九十百千万\d]+[章节]/g },
    { name: 'Chapter X', regex: /Chapter\s*\d+/gi },
    { name: '数字章节', regex: /第\d+章/g },
    { name: '数字序号', regex: /^\d+[\.\、]/gm },
    { name: '中文序号', regex: /[一二三四五六七八九十]+、/g }
  ]

  let bestPattern = null
  let maxMatches = 0

  patterns.forEach(pattern => {
    const matches = content.match(pattern.regex) || []
    if (matches.length > maxMatches) {
      maxMatches = matches.length
      bestPattern = pattern
    }
  })

  return {
    totalLength: content.length,
    estimatedChapters: maxMatches,
    bestPattern: bestPattern,
    needsSegmentation: content.length > 100000 && processingMode.value === 'large'
  }
}

// 识别章节
const identifyChapters = async (content, analysis) => {
  const chapters = []

  if (processingMode.value === 'auto' || processingMode.value === 'large') {
    // 智能识别模式
    if (largeConfig.value.useAI && await hasAIConfig()) {
      addLog('info', '使用AI进行智能章节识别...')
      try {
        const aiChapters = await identifyChaptersWithAI(content)
        if (aiChapters && aiChapters.length > 0) {
          chapters.push(...aiChapters)
          detectedChapters.value = chapters.length
          return chapters
        }
      } catch (error) {
        addLog('warning', `AI识别失败，降级到本地算法: ${error.message}`)
      }
    }

    // 本地算法识别
    const localChapters = identifyChaptersLocally(content, analysis)
    chapters.push(...localChapters)

  } else if (processingMode.value === 'manual') {
    // 手动规则模式
    const manualChapters = identifyChaptersManually(content)
    chapters.push(...manualChapters)
  }

  detectedChapters.value = chapters.length
  addLog('success', `章节识别完成，共找到 ${chapters.length} 个章节`)

  return chapters
}

// 本地章节识别
const identifyChaptersLocally = (content, analysis) => {
  const chapters = []

  if (analysis.bestPattern && analysis.estimatedChapters > 0) {
    // 使用检测到的最佳模式
    const matches = [...content.matchAll(analysis.bestPattern.regex)]

    for (let i = 0; i < matches.length; i++) {
      const match = matches[i]
      const nextMatch = matches[i + 1]

      const startIndex = match.index
      const endIndex = nextMatch ? nextMatch.index : content.length
      const chapterContent = content.slice(startIndex, endIndex).trim()

      if (chapterContent.length > 100) {
        chapters.push({
          index: i,
          title: match[0].trim(),
          content: chapterContent,
          wordCount: chapterContent.length,
          startIndex: startIndex,
          endIndex: endIndex,
          summary: generateChapterSummary(chapterContent)
        })
      }
    }
  } else {
    // 没有明显章节标记，智能分割
    const avgChapterLength = Math.max(3000, Math.floor(content.length / 20))
    let currentPos = 0
    let chapterIndex = 0

    while (currentPos < content.length) {
      const endPos = Math.min(currentPos + avgChapterLength, content.length)

      // 寻找合适的断点
      let breakPoint = endPos
      for (let i = endPos; i < Math.min(endPos + 1000, content.length); i++) {
        if (content[i] === '。' || content[i] === '\n\n' || content[i] === '！' || content[i] === '？') {
          breakPoint = i + 1
          break
        }
      }

      const chapterContent = content.slice(currentPos, breakPoint).trim()
      if (chapterContent.length > 500) {
        chapters.push({
          index: chapterIndex,
          title: `第${chapterIndex + 1}章`,
          content: chapterContent,
          wordCount: chapterContent.length,
          startIndex: currentPos,
          endIndex: breakPoint,
          summary: generateChapterSummary(chapterContent),
          autoGenerated: true
        })
        chapterIndex++
      }

      currentPos = breakPoint
    }
  }

  return chapters
}

// 手动规则识别
const identifyChaptersManually = (content) => {
  const chapters = []
  let pattern

  // 根据选择的模式构建正则表达式
  switch (manualConfig.value.chapterPattern) {
    case 'chapter_num':
      pattern = /第[一二三四五六七八九十百千万\d]+[章节][^\n]*/g
      break
    case 'chapter_title':
      pattern = /第[一二三四五六七八九十百千万\d]+[章节][^\n]*/g
      break
    case 'number_dot':
      pattern = /^\d+[\.\、][^\n]*/gm
      break
    case 'custom':
      if (manualConfig.value.customPattern) {
        try {
          pattern = new RegExp(manualConfig.value.customPattern, 'g')
        } catch (error) {
          addLog('error', '自定义正则表达式格式错误')
          return chapters
        }
      }
      break
    default:
      pattern = /第[一二三四五六七八九十百千万\d]+[章节][^\n]*/g
  }

  if (pattern) {
    const matches = [...content.matchAll(pattern)]

    for (let i = 0; i < matches.length; i++) {
      const match = matches[i]
      const nextMatch = matches[i + 1]

      const startIndex = match.index
      const endIndex = nextMatch ? nextMatch.index : content.length
      const chapterContent = content.slice(startIndex, endIndex).trim()

      if (chapterContent.length >= manualConfig.value.minChapterLength) {
        chapters.push({
          index: i,
          title: match[0].trim(),
          content: chapterContent,
          wordCount: chapterContent.length,
          startIndex: startIndex,
          endIndex: endIndex,
          summary: generateChapterSummary(chapterContent)
        })
      }
    }
  }

  return chapters
}

// 生成章节摘要
const generateChapterSummary = (content) => {
  if (!content || content.length < 100) return '内容较短'

  // 提取前150字作为摘要
  const summary = content.slice(0, 150).replace(/\n/g, ' ').trim()
  return summary + (content.length > 150 ? '...' : '')
}

// 检查是否有AI配置
const hasAIConfig = async () => {
  try {
    const apiConfig = localStorage.getItem('apiConfig')
    if (!apiConfig) return false

    const config = JSON.parse(apiConfig)
    return !!(config.apiKey && config.baseUrl)
  } catch (error) {
    return false
  }
}

// AI章节识别
const identifyChaptersWithAI = async (content) => {
  try {
    const apiConfig = JSON.parse(localStorage.getItem('apiConfig'))

    const prompt = `请分析以下小说文本，智能识别其中的章节结构。

文本内容：
${content.slice(0, 10000)}${content.length > 10000 ? '\n...(内容过长，已截取前10000字)' : ''}

请按照以下要求进行分析：
1. 识别明显的章节标题（如"第X章"、"Chapter X"等）
2. 如果没有明显标题，根据内容逻辑划分合理的章节
3. 每个章节应该有完整的情节或主题
4. 为每个章节生成简洁的标题和摘要

请以JSON格式返回结果：
{
  "chapters": [
    {
      "title": "章节标题",
      "startIndex": 开始位置,
      "endIndex": 结束位置,
      "summary": "章节简介"
    }
  ]
}`

    const response = await fetch(`${apiConfig.baseUrl}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiConfig.apiKey}`
      },
      body: JSON.stringify({
        model: apiConfig.model || 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 3000,
        temperature: 0.3
      })
    })

    if (!response.ok) {
      throw new Error(`AI API请求失败: ${response.status}`)
    }

    const data = await response.json()
    const aiResult = data.choices[0].message.content

    // 解析AI返回的JSON
    const parsed = JSON.parse(aiResult)
    if (parsed.chapters && Array.isArray(parsed.chapters)) {
      return parsed.chapters.map((chapter, index) => ({
        index: index,
        title: chapter.title || `第${index + 1}章`,
        startIndex: chapter.startIndex || 0,
        endIndex: chapter.endIndex || content.length,
        summary: chapter.summary || '',
        content: content.slice(chapter.startIndex || 0, chapter.endIndex || content.length),
        wordCount: (chapter.endIndex || content.length) - (chapter.startIndex || 0),
        aiGenerated: true
      }))
    }

    return null
  } catch (error) {
    throw new Error(`AI处理失败: ${error.message}`)
  }
}

// 创建文件分段
const createFileSegments = (content, analysis) => {
  const segments = []
  const chunkSize = largeConfig.value.chunkSize
  const overlapSize = largeConfig.value.overlapSize

  let start = 0
  let segmentIndex = 0

  while (start < content.length) {
    const end = Math.min(start + chunkSize, content.length)
    const segmentContent = content.slice(start, end)

    segments.push({
      index: segmentIndex,
      content: segmentContent,
      size: segmentContent.length,
      startIndex: start,
      endIndex: end
    })

    start = end - overlapSize
    segmentIndex++
  }

  addLog('info', `创建了 ${segments.length} 个处理分段`)
  return segments
}

// 处理文件分段
const processFileSegments = async (segments) => {
  const results = []

  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i]

    try {
      addLog('info', `正在处理第 ${i + 1}/${segments.length} 段...`)

      // 更新处理速度
      if (startTime.value > 0) {
        const elapsed = (Date.now() - startTime.value) / 1000
        const speed = Math.floor(processedWords.value / elapsed)
        processingSpeed.value = `${speed.toLocaleString()} 字/秒`
      }

      // 识别该分段的章节
      const chapters = await identifyChapters(segment.content, { bestPattern: null, estimatedChapters: 0 })

      results.push({
        segmentIndex: i,
        chapters: chapters,
        startIndex: segment.startIndex,
        endIndex: segment.endIndex
      })

      addLog('success', `第 ${i + 1} 段处理完成，识别到 ${chapters.length} 个章节`)

    } catch (error) {
      addLog('error', `第 ${i + 1} 段处理失败: ${error.message}`)
      results.push({
        segmentIndex: i,
        chapters: [],
        error: error.message,
        startIndex: segment.startIndex,
        endIndex: segment.endIndex
      })
    }
  }

  return results
}

// 合并分段章节
const mergeSegmentChapters = (segmentResults) => {
  const allChapters = []
  let globalIndex = 0

  for (const result of segmentResults) {
    if (result.chapters && result.chapters.length > 0) {
      for (const chapter of result.chapters) {
        // 检查是否与前一个章节重叠
        let shouldAdd = true
        if (allChapters.length > 0) {
          const lastChapter = allChapters[allChapters.length - 1]
          if (isSimilarChapter(lastChapter, chapter)) {
            shouldAdd = false
            addLog('info', `跳过重复章节: ${chapter.title}`)
          }
        }

        if (shouldAdd) {
          allChapters.push({
            ...chapter,
            index: globalIndex,
            globalStartIndex: result.startIndex + (chapter.startIndex || 0),
            globalEndIndex: result.startIndex + (chapter.endIndex || chapter.content.length)
          })
          globalIndex++
        }
      }
    }
  }

  return allChapters
}

// 检查章节是否相似
const isSimilarChapter = (chapter1, chapter2) => {
  // 标题相似度检查
  if (chapter1.title && chapter2.title) {
    const title1 = chapter1.title.toLowerCase().trim()
    const title2 = chapter2.title.toLowerCase().trim()
    if (title1 === title2) return true
  }

  // 内容重叠检查
  if (chapter1.content && chapter2.content) {
    const content1 = chapter1.content.slice(0, 200)
    const content2 = chapter2.content.slice(0, 200)
    const similarity = calculateSimilarity(content1, content2)
    if (similarity > 0.8) return true
  }

  return false
}

// 计算文本相似度
const calculateSimilarity = (text1, text2) => {
  if (!text1 || !text2) return 0

  const len1 = text1.length
  const len2 = text2.length
  const maxLen = Math.max(len1, len2)

  if (maxLen === 0) return 1

  let matches = 0
  const minLen = Math.min(len1, len2)

  for (let i = 0; i < minLen; i++) {
    if (text1[i] === text2[i]) {
      matches++
    }
  }

  return matches / maxLen
}

// 生成拆书结果
const generateSplitResult = (chapters, originalContent) => {
  const totalWords = originalContent.length
  const processingTime = startTime.value > 0 ?
    Math.round((Date.now() - startTime.value) / 1000) : 0

  // 计算识别准确率
  let accuracy = 85 // 基础准确率
  if (chapters.some(ch => ch.aiGenerated)) {
    accuracy = 95 // AI识别准确率更高
  }
  if (chapters.some(ch => ch.autoGenerated)) {
    accuracy = 75 // 自动生成准确率较低
  }

  return {
    chapters: chapters,
    totalWords: totalWords,
    processingTime: `${Math.floor(processingTime / 60)}分${processingTime % 60}秒`,
    accuracy: accuracy,
    timestamp: new Date().toLocaleString(),
    mode: processingMode.value,
    encoding: selectedEncoding.value
  }
}

// 取消处理
const cancelProcessing = async () => {
  try {
    await ElMessageBox.confirm('确定要取消拆书处理吗？', '确认取消', {
      type: 'warning'
    })

    isProcessing.value = false
    addLog('warning', '用户取消了拆书处理')
    ElMessage.info('已取消拆书处理')

  } catch (error) {
    // 用户取消确认
  }
}

// 预览章节
const previewChapter = (chapter) => {
  previewChapterData.value = chapter
  showChapterPreview.value = true
  emit('chapter-selected', chapter)
}

// 编辑章节
const editChapter = (chapter) => {
  // 这里可以打开章节编辑对话框
  ElMessage.info('章节编辑功能开发中...')
}

// 导出章节
const exportChapters = () => {
  if (!splitResult.value) return

  try {
    let exportContent = `《${selectedFile.value.name}》拆书结果\n`
    exportContent += `${'='.repeat(50)}\n\n`
    exportContent += `拆书时间：${splitResult.value.timestamp}\n`
    exportContent += `总章节数：${splitResult.value.chapters.length}\n`
    exportContent += `总字数：${splitResult.value.totalWords.toLocaleString()}\n`
    exportContent += `处理时间：${splitResult.value.processingTime}\n`
    exportContent += `识别准确率：${splitResult.value.accuracy}%\n`
    exportContent += `处理模式：${splitResult.value.mode}\n\n`
    exportContent += `${'='.repeat(50)}\n\n`

    splitResult.value.chapters.forEach((chapter, index) => {
      exportContent += `第${index + 1}章：${chapter.title}\n`
      exportContent += `字数：${chapter.wordCount.toLocaleString()}\n`
      if (chapter.summary) {
        exportContent += `摘要：${chapter.summary}\n`
      }
      exportContent += `${'-'.repeat(30)}\n`
      exportContent += `${chapter.content}\n\n`
      exportContent += `${'='.repeat(50)}\n\n`
    })

    const blob = new Blob([exportContent], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `拆书结果_${selectedFile.value.name}_${new Date().getTime()}.txt`
    link.click()
    URL.revokeObjectURL(url)

    ElMessage.success('章节导出成功！')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 保存到小说库
const saveToLibrary = () => {
  if (!splitResult.value) return

  // 这里可以集成到小说管理功能
  ElMessage.success('已保存到小说库！')
}

// 重新开始
const startOver = () => {
  selectedFile.value = null
  splitResult.value = null
  isProcessing.value = false
  resetProgress()
  processingLogs.value = []
  addLog('info', '重新开始拆书')
}

// 生命周期
onMounted(() => {
  addLog('info', '智能拆书组件已就绪')
})
</script>

<style scoped>
.smart-book-splitter {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 上传卡片样式 */
.upload-card {
  border: 2px solid #e4e7ed;
  transition: all 0.3s;
}

.upload-card:hover {
  border-color: #409eff;
}

.card-header {
  text-align: center;
}

.card-header h3 {
  margin: 0 0 8px 0;
  color: #409eff;
  font-size: 24px;
}

.card-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 模式选择样式 */
.mode-selection {
  margin: 20px 0;
  text-align: center;
}

.mode-description {
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}

/* 上传区域样式 */
.upload-area {
  margin: 20px 0;
}

.upload-area .el-upload-dragger {
  width: 100%;
  height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s;
}

.upload-area .el-upload-dragger:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.el-upload__tip .highlight {
  color: #409eff;
  font-weight: bold;
}

.size-tip {
  color: #909399;
  font-size: 12px;
}

/* 文件信息样式 */
.file-info {
  margin-top: 20px;
}

.file-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin: 16px 0;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-row .label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.detail-row .value {
  color: #303133;
}

.detail-row .value.strategy {
  color: #409eff;
  font-weight: bold;
}

.encoding-options {
  display: flex;
  gap: 20px;
  justify-content: center;
}

/* 配置区域样式 */
.split-config {
  margin: 20px 0;
}

.manual-config,
.large-config {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
}

/* 进度区域样式 */
.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-header h3 {
  margin: 0;
  color: #409eff;
}

.overall-progress {
  margin: 20px 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.progress-percent {
  font-weight: bold;
  color: #409eff;
}

.current-step-text {
  text-align: center;
  margin-top: 8px;
  color: #909399;
  font-size: 14px;
}

/* 实时统计样式 */
.real-time-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin: 20px 0;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

/* 处理日志样式 */
.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: #f5f7fa;
  border-radius: 6px;
  padding: 12px;
}

.log-item {
  display: flex;
  gap: 12px;
  margin: 4px 0;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #909399;
  min-width: 80px;
  font-family: monospace;
}

.log-message {
  color: #606266;
}

.log-item.success .log-message {
  color: #67c23a;
}

.log-item.error .log-message {
  color: #f56c6c;
}

.log-item.warning .log-message {
  color: #e6a23c;
}

.cancel-section {
  text-align: center;
  margin-top: 20px;
}

/* 结果区域样式 */
.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-header h3 {
  margin: 0;
  color: #67c23a;
}

.result-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin: 20px 0;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.stat-card:hover {
  background: #ecf5ff;
  border-color: #b3d8ff;
}

.stat-icon {
  font-size: 32px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-text {
  font-size: 12px;
  color: #909399;
}

/* 章节预览样式 */
.chapter-preview {
  margin-top: 20px;
}

.preview-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chapter-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.chapter-list.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.chapter-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s;
}

.chapter-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.chapter-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.chapter-number {
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  min-width: 30px;
  text-align: center;
}

.chapter-title {
  flex: 1;
  font-weight: bold;
  color: #303133;
}

.chapter-words {
  font-size: 12px;
  color: #909399;
}

.chapter-summary {
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 12px;
}

.chapter-actions {
  display: flex;
  gap: 8px;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
}

/* 章节预览对话框样式 */
.chapter-preview-content {
  max-height: 60vh;
  overflow-y: auto;
}

.preview-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-header h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.preview-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.preview-text {
  line-height: 1.8;
  color: #606266;
  white-space: pre-wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .smart-book-splitter {
    padding: 10px;
  }

  .file-details {
    grid-template-columns: 1fr;
  }

  .result-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .real-time-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .chapter-list.grid {
    grid-template-columns: 1fr;
  }

  .preview-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .result-actions {
    flex-direction: column;
  }
}
</style>
