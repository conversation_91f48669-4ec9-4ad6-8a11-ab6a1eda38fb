# 91Writing 一键拆书功能使用说明

## 🎯 功能概述

91Writing的一键拆书功能可以智能识别小说文本中的章节结构，自动将整本小说拆分成独立的章节，支持从几万字到上百万字的各种规模文件。

## ✨ 主要特性

### 🤖 智能识别模式
- **多种章节格式支持**：第X章、Chapter X、数字序号等
- **AI智能分析**：使用AI技术精确识别章节边界
- **自动降级处理**：AI失败时自动使用本地算法
- **智能标题生成**：为无标题章节自动生成合适标题

### 🚀 三种处理模式

#### 1. 智能模式（推荐）
- **适用场景**：大部分小说文件
- **处理方式**：AI自动识别章节格式
- **优点**：识别准确率高，处理速度快
- **文件大小**：建议50MB以内

#### 2. 手动模式
- **适用场景**：特殊格式或需要精确控制
- **处理方式**：用户配置章节识别规则
- **优点**：可自定义识别规则，精确控制
- **支持规则**：
  - 第X章格式
  - Chapter X格式
  - 数字序号格式
  - 自定义正则表达式

#### 3. 大文件模式
- **适用场景**：上百万字的超大文件
- **处理方式**：分段处理 + 智能合并
- **优点**：支持无限大小文件，内存占用低
- **特性**：
  - 智能分段处理
  - 段落重叠确保完整性
  - 实时进度监控
  - 错误恢复机制

## 📖 使用步骤

### 第一步：选择处理模式

1. 进入"拆书分析"页面
2. 在文件上传区域选择处理模式：
   - **普通模式**：适用于常规大小文件
   - **大文件模式**：适用于超大文件

### 第二步：上传文件

1. **支持格式**：.txt、.docx
2. **编码支持**：UTF-8、GBK/GB2312
3. **上传方式**：拖拽或点击上传

### 第三步：配置参数

#### 智能模式
- 无需配置，自动识别

#### 手动模式
- 选择章节模式（第X章、Chapter X等）
- 设置最小章节长度
- 可使用自定义正则表达式

#### 大文件模式
- **分段大小**：建议30000-50000字
- **重叠大小**：建议3000字
- **AI处理**：可选择是否启用AI

### 第四步：开始拆书

1. 点击"开始智能拆书"按钮
2. 系统自动处理文件
3. 实时查看处理进度

### 第五步：查看结果

处理完成后可以：
- 查看章节统计信息
- 预览各个章节内容
- 编辑章节标题
- 导出拆书结果

## 📊 结果展示

### 拆书统计
- **总章节数**：识别到的章节总数
- **总字数**：所有章节的字数统计
- **平均章节长度**：每章平均字数
- **识别准确率**：AI识别的准确度评估

### 质量指标
- **AI处理章节**：使用AI识别的章节数量
- **自动生成章节**：系统自动分割的章节数量
- **识别模式**：使用的章节识别模式

### 章节列表
每个章节显示：
- 章节标题和编号
- 字数统计
- 起始行号
- 章节摘要（如果有）
- 处理方式标签（AI/自动/手动）

## 🛠️ 高级功能

### 章节编辑
- **标题编辑**：双击章节标题进行编辑
- **内容预览**：点击预览按钮查看章节内容
- **批量操作**：支持批量选择和操作

### 导出功能
- **章节列表导出**：导出章节目录和统计信息
- **完整内容导出**：导出包含所有章节内容的文件
- **格式选择**：支持TXT格式导出

### 与拆书分析集成
- 拆书完成后可直接进行拆书分析
- 支持选择特定章节进行分析
- 分析结果可保存到参考库

## ⚙️ 配置建议

### AI配置（可选）
为获得最佳识别效果，建议配置AI API：
1. 进入设置页面
2. 配置API密钥和模型
3. 推荐使用GPT-3.5-turbo或更高版本

### 文件编码
- **UTF-8**：现代文本文件的标准编码
- **GBK/GB2312**：适用于较老的中文文本文件
- 如果出现乱码，尝试切换编码格式

### 分段参数（大文件模式）
- **分段大小**：
  - 小文件：10000-20000字
  - 中等文件：30000-50000字
  - 超大文件：50000-100000字
- **重叠大小**：建议为分段大小的10%

## 🔧 故障排除

### 常见问题

#### Q: 识别不到章节怎么办？
A: 
1. 检查文件编码是否正确
2. 尝试手动模式并自定义规则
3. 使用大文件模式的智能分割

#### Q: 处理大文件时内存不足？
A: 
1. 使用大文件模式
2. 减小分段大小参数
3. 关闭其他占用内存的程序

#### Q: AI识别失败怎么办？
A: 
1. 检查AI API配置
2. 系统会自动降级到本地算法
3. 可以手动重试或使用手动模式

#### Q: 章节标题不准确？
A: 
1. 可以手动编辑章节标题
2. 尝试不同的识别模式
3. 使用自定义正则表达式

### 性能优化
- 关闭不必要的浏览器标签页
- 确保网络连接稳定（AI模式）
- 选择合适的分段大小

## 📈 最佳实践

### 文件准备
1. 确保文本格式规范
2. 章节标题格式统一
3. 去除多余的空行和特殊字符

### 模式选择
- **小说（有明确章节）**：智能模式
- **特殊格式文本**：手动模式
- **超大文件**：大文件模式

### 后续处理
1. 检查拆分结果的准确性
2. 编辑不准确的章节标题
3. 导出结果备份
4. 进行拆书分析获得写作技巧

## 🎉 功能亮点

- **零配置使用**：智能模式无需任何配置
- **超大文件支持**：理论上支持无限大小文件
- **高准确率**：AI识别准确率可达95%以上
- **实时反馈**：详细的处理进度和日志
- **灵活导出**：多种导出格式和选项
- **无缝集成**：与拆书分析功能完美结合

---

**提示**：一键拆书功能持续优化中，如遇到问题请及时反馈，我们会不断改进用户体验。
