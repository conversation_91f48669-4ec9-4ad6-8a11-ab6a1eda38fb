<template>
  <div class="tools-library">
    
    <div class="tools-grid">
      <!-- 智能拆书分析 -->
      <div class="tool-card" @click="openTool('bookSplit')">
        <div class="tool-icon">📚</div>
        <h3>智能拆书分析</h3>
        <p>智能识别章节结构，深度分析小说内容</p>
      </div>

      <!-- 全自动拆书 -->
      <div class="tool-card" @click="openTool('autoBookSplit')">
        <div class="tool-icon">⚡</div>
        <h3>全自动拆书</h3>
        <p>深度拆解书籍写作方法、技巧、结构和用词，学习作者创作精髓</p>
      </div>

      <!-- 章节结构分析 -->
      <div class="tool-card" @click="openTool('chapterAnalysis')">
        <div class="tool-icon">📖</div>
        <h3>章节结构分析</h3>
        <p>分析小说章节结构和内容组织</p>
      </div>

      <!-- 写作技巧提取 -->
      <div class="tool-card" @click="openTool('writingTechniques')">
        <div class="tool-icon">✍️</div>
        <h3>写作技巧提取</h3>
        <p>从优秀作品中提取写作技巧和方法</p>
      </div>

      <!-- 人物关系分析 -->
      <div class="tool-card" @click="openTool('characterAnalysis')">
        <div class="tool-icon">👥</div>
        <h3>人物关系分析</h3>
        <p>分析小说中的人物关系和角色发展</p>
      </div>

      <!-- 情节结构分析 -->
      <div class="tool-card" @click="openTool('plotAnalysis')">
        <div class="tool-icon">🎭</div>
        <h3>情节结构分析</h3>
        <p>分析故事情节发展和结构安排</p>
      </div>
    </div>
    
    <!-- 工具对话框 -->
    <el-dialog 
      v-model="showToolDialog" 
      :title="currentTool.title"
      width="900px"
      class="tool-dialog"
      :close-on-click-modal="false"
    >
      <div class="tool-content">
        <div class="tool-form">
          <el-form :model="toolForm" label-width="100px" @submit.prevent="generateContent">
            <el-form-item v-for="field in currentTool.fields" :key="field.key" :label="field.label">
              <!-- 小说选择器 -->
              <el-select 
                v-if="field.type === 'novel-select'"
                v-model="toolForm[field.key]" 
                :placeholder="field.placeholder"
                @change="onNovelChange"
                clearable
              >
                <el-option 
                  v-for="novel in novelList" 
                  :key="novel.value" 
                  :label="novel.label" 
                  :value="novel.value"
                />
              </el-select>
              
              <!-- 章节多选器 -->
              <el-select 
                v-else-if="field.type === 'chapter-select'"
                v-model="toolForm[field.key]" 
                :placeholder="field.placeholder"
                multiple
                collapse-tags
                collapse-tags-tooltip
                :disabled="!toolForm.selectedNovel"
                clearable
              >
                <el-option 
                  v-for="chapter in selectedNovelChapters" 
                  :key="chapter.value" 
                  :label="chapter.label" 
                  :value="chapter.value"
                />
              </el-select>
              
              <!-- 普通输入框 -->
              <el-input 
                v-else-if="field.type === 'input'"
                v-model="toolForm[field.key]" 
                :placeholder="field.placeholder"
                :type="field.key === 'count' && currentToolType === 'character' ? 'number' : 'text'"
                :min="field.key === 'count' && currentToolType === 'character' ? 1 : undefined"
                :max="field.key === 'count' && currentToolType === 'character' ? 15 : undefined"
                @keyup.enter="generateContent"
                @input="validateCharacterCount(field, $event)"
              />
              <!-- 角色数量提示 -->
              <div 
                v-if="field.key === 'count' && currentToolType === 'character' && toolForm[field.key]"
                class="character-count-hint"
              >
                <span v-if="isValidCharacterCount(toolForm[field.key])" class="valid-hint">
                  ✓ 将生成 {{ toolForm[field.key] }} 个角色
                </span>
                <span v-else class="invalid-hint">
                  ⚠️ 请输入1-15之间的数字
                </span>
              </div>
              
              <!-- 文本域 -->
              <el-input 
                v-else-if="field.type === 'textarea'"
                v-model="toolForm[field.key]" 
                type="textarea" 
                :rows="4"
                :placeholder="field.placeholder"
              />
              
              <!-- 提示词选择器 -->
              <el-select 
                v-else-if="field.type === 'prompt-select'"
                v-model="toolForm[field.key]" 
                :placeholder="field.placeholder"
                clearable
                filterable
                @change="onPromptChange"
              >
                <el-option 
                  v-for="prompt in getPromptsByCategory(field.category)" 
                  :key="prompt.id" 
                  :label="prompt.title" 
                  :value="prompt.id"
                >
                  <div class="prompt-option">
                    <div class="prompt-option-title">{{ prompt.title }}</div>
                    <div class="prompt-option-desc">{{ prompt.description }}</div>
                  </div>
                </el-option>
              </el-select>
              
              <!-- 下拉选择 -->
              <el-select
                v-else-if="field.type === 'select'"
                v-model="toolForm[field.key]"
                :placeholder="field.placeholder"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <!-- 文件上传 -->
              <el-upload
                v-else-if="field.type === 'file'"
                class="upload-demo"
                drag
                :auto-upload="false"
                :on-change="handleFileChange"
                :show-file-list="false"
                accept=".txt,.docx"
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    支持 .txt 和 .docx 格式，文件大小不限
                  </div>
                </template>
              </el-upload>

              <!-- 显示已选择的文件 -->
              <div v-if="field.type === 'file' && selectedFile" class="selected-file">
                <el-icon><Document /></el-icon>
                <span>{{ selectedFile.name }}</span>
                <span class="file-size">({{ (selectedFile.size / 1024).toFixed(1) }}KB)</span>
                <el-button type="text" @click="removeFile" class="remove-btn">
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="tool-actions">
          <el-button type="primary" @click="generateContent" :loading="generating" :disabled="!canGenerate">
            <el-icon><MagicStick /></el-icon>
            {{ generating ? '生成中...' : '生成内容' }}
          </el-button>
          <el-button @click="clearForm" :disabled="generating">
            清空
          </el-button>
        </div>
        
        <!-- 生成进度提示 -->
        <div v-if="generating" class="generating-status">
          <el-progress :percentage="generatingProgress" :show-text="false" />
          <span class="status-text">{{ generatingStatusText }}</span>
        </div>
        
        <!-- 结果显示区 -->
        <div class="tool-result" v-if="generatedContent || generating">
          <h4>生成结果：</h4>
          <div class="result-content-wrapper">
            <el-input
              v-model="displayContent"
              type="textarea"
              :rows="15"
              readonly
              class="result-textarea"
              placeholder="生成的内容将在这里显示..."
              ref="resultTextarea"
            />
          </div>
          <div class="result-actions" v-if="generatedContent && !generating">
            <el-button @click="copyToClipboard" :disabled="!generatedContent">
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
            <el-button @click="saveResult" :disabled="!generatedContent">
              <el-icon><DocumentAdd /></el-icon>
              保存到本地
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 全自动拆书对话框 -->
    <el-dialog
      v-model="showAutoSplitDialog"
      title="⚡ 全自动深度拆书分析"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="auto-split-content">
        <div v-if="!isAutoSplitting" class="upload-section">
          <div class="section-header">
            <h3>📚 上传要拆解的书籍文件</h3>
            <p>深度拆解书籍的写作方法、技巧、结构和用词，学习作者的创作精髓</p>
          </div>

          <el-upload
            class="auto-upload"
            drag
            :auto-upload="false"
            :on-change="handleAutoSplitFile"
            :show-file-list="false"
            accept=".txt,.docx"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将小说文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                全自动模式将自动完成：文件解析 → 章节识别 → 内容分析 → 保存到小说库
              </div>
            </template>
          </el-upload>

          <div class="auto-features">
            <h4>🤖 全自动功能特性</h4>
            <ul>
              <li>✅ 智能识别章节结构（支持多种格式）</li>
              <li>✅ 自动生成章节标题和摘要</li>
              <li>✅ 智能分段处理大文件</li>
              <li>✅ 自动保存到小说库</li>
              <li>✅ 生成完整的拆书报告</li>
            </ul>
          </div>
        </div>

        <div v-else class="processing-section">
          <div class="progress-header">
            <h3>🚀 正在全自动处理...</h3>
            <p>{{ autoSplitStatus }}</p>
          </div>

          <el-progress
            :percentage="autoSplitProgress"
            :stroke-width="20"
            status="success"
          />

          <div class="processing-steps">
            <div class="step" :class="{ active: autoSplitProgress >= 20 }">
              <div class="step-icon">📄</div>
              <div class="step-text">解析文件</div>
            </div>
            <div class="step" :class="{ active: autoSplitProgress >= 40 }">
              <div class="step-icon">🔍</div>
              <div class="step-text">识别章节</div>
            </div>
            <div class="step" :class="{ active: autoSplitProgress >= 60 }">
              <div class="step-icon">🤖</div>
              <div class="step-text">AI分析</div>
            </div>
            <div class="step" :class="{ active: autoSplitProgress >= 80 }">
              <div class="step-icon">💾</div>
              <div class="step-text">保存数据</div>
            </div>
            <div class="step" :class="{ active: autoSplitProgress >= 100 }">
              <div class="step-icon">✅</div>
              <div class="step-text">完成</div>
            </div>
          </div>
        </div>

        <div v-if="autoSplitResult" class="result-section">
          <div class="result-header">
            <h3>🎉 全自动拆书完成！</h3>
          </div>

          <div class="result-stats">
            <div class="stat-card">
              <div class="stat-icon">📖</div>
              <div class="stat-content">
                <div class="stat-number">{{ autoSplitResult.totalChapters }}</div>
                <div class="stat-label">识别章节</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">📝</div>
              <div class="stat-content">
                <div class="stat-number">{{ autoSplitResult.totalWords?.toLocaleString() }}</div>
                <div class="stat-label">总字数</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">⏱️</div>
              <div class="stat-content">
                <div class="stat-number">{{ autoSplitResult.processingTime }}</div>
                <div class="stat-label">处理时间</div>
              </div>
            </div>
          </div>

          <div class="result-actions">
            <el-button type="primary" @click="viewAutoSplitResult">
              <el-icon><Document /></el-icon>
              查看详细结果
            </el-button>
            <el-button @click="resetAutoSplit">
              重新开始
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAutoSplitDialog = false" :disabled="isAutoSplitting">
            {{ isAutoSplitting ? '处理中...' : '关闭' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { MagicStick, Refresh, CopyDocument, DocumentAdd, UploadFilled, Document, Close } from '@element-plus/icons-vue'
import { useNovelStore } from '@/stores/novel'

const novelStore = useNovelStore()

const showToolDialog = ref(false)
const generating = ref(false)
const generatedContent = ref('')
const generatingProgress = ref(0)
const generatingStatusText = ref('')
const toolForm = reactive({})
const resultTextarea = ref(null)

// 小说列表数据
const novelList = ref([])
const selectedNovelChapters = ref([])

// 提示词数据
const availablePrompts = ref([])
const selectedPromptData = ref(null)

// 文件上传相关
const selectedFile = ref(null)
const splitResult = ref(null)

// 全自动拆书相关
const showAutoSplitDialog = ref(false)
const autoSplitProgress = ref(0)
const autoSplitStatus = ref('')
const autoSplitResult = ref(null)
const isAutoSplitting = ref(false)

// 加载小说列表
const loadNovelList = () => {
  try {
    const savedNovels = JSON.parse(localStorage.getItem('novels') || '[]')
    console.log('原始小说数据:', savedNovels) // 调试用
    
    if (!Array.isArray(savedNovels)) {
      console.warn('小说数据不是数组格式')
      novelList.value = []
      return
    }
    
    novelList.value = savedNovels.map(novel => {
      if (!novel || typeof novel !== 'object') {
        return null
      }
      
      return {
        value: novel.id || `novel_${Date.now()}_${Math.random()}`,
        label: novel.title || '未命名小说',
        chapters: Array.isArray(novel.chapterList) ? novel.chapterList : (Array.isArray(novel.chapters) ? novel.chapters : [])
      }
    }).filter(novel => novel !== null) // 过滤掉无效的小说
    
    console.log('处理后的小说列表:', novelList.value) // 调试用
  } catch (error) {
    console.error('加载小说列表失败:', error)
    novelList.value = []
  }
}

// 当选择小说时，更新章节列表
const onNovelChange = (novelId) => {
  console.log('选择的小说ID:', novelId) // 调试用
  const selectedNovel = novelList.value.find(novel => novel.value === novelId)
  console.log('找到的小说:', selectedNovel) // 调试用
  
  if (selectedNovel && selectedNovel.chapters && Array.isArray(selectedNovel.chapters)) {
    console.log('小说章节数据:', selectedNovel.chapters) // 调试用
    selectedNovelChapters.value = selectedNovel.chapters.map(chapter => {
      if (!chapter || typeof chapter !== 'object') {
        return null
      }
      return {
        value: chapter.id || `chapter_${Date.now()}_${Math.random()}`,
        label: chapter.title || '未命名章节',
        content: chapter.content || '',
        description: chapter.description || ''
      }
    }).filter(chapter => chapter !== null)
  } else {
    console.log('没有找到有效的章节数据') // 调试用
    selectedNovelChapters.value = []
  }
  
  // 清空已选择的章节
  if (toolForm.selectedChapters) {
    toolForm.selectedChapters = []
  }
}

// 工具配置
const toolsConfig = {
  outline: {
    title: '细纲生成器',
    hasNovelSelector: true, // 标记需要小说选择器
    fields: [
      { key: 'selectedNovel', label: '选择小说', type: 'novel-select', placeholder: '请选择要生成细纲的小说', required: true },
      { key: 'selectedChapters', label: '参考章节', type: 'chapter-select', placeholder: '选择要参考的章节（可多选）' },
      { key: 'selectedPrompt', label: '提示词模板', type: 'prompt-select', placeholder: '选择提示词模板（可选）', category: 'outline' },
      { key: 'chapters', label: '章节数量', type: 'input', placeholder: '预计章节数（建议5-15章）' }
    ]
  },
  cheat: {
    title: '金手指生成器',
    hasNovelSelector: true, // 标记需要小说选择器
    fields: [
      { key: 'selectedNovel', label: '选择小说', type: 'novel-select', placeholder: '请选择要生成金手指的小说', required: true },
      { key: 'selectedChapters', label: '参考章节', type: 'chapter-select', placeholder: '选择要参考的章节（可多选）' },
      { key: 'selectedPrompt', label: '提示词模板', type: 'prompt-select', placeholder: '选择提示词模板（可选）', category: 'cheat' },
      { key: 'level', label: '能力等级', type: 'select', placeholder: '选择等级', required: true, options: [
        { label: '初级', value: 'low' },
        { label: '中级', value: 'medium' },
        { label: '高级', value: 'high' },
        { label: '神级', value: 'god' }
      ]},
      { key: 'description', label: '特殊要求', type: 'textarea', placeholder: '描述特殊要求或偏好（可选）' }
    ]
  },
  opening: {
    title: '黄金开篇生成器',
    fields: [
      { key: 'selectedPrompt', label: '提示词模板', type: 'prompt-select', placeholder: '选择提示词模板（可选）', category: 'opening' },
      { key: 'genre', label: '小说类型', type: 'select', placeholder: '选择类型', required: true, options: [
        { label: '都市', value: 'urban' },
        { label: '玄幻', value: 'fantasy' },
        { label: '悬疑', value: 'mystery' },
        { label: '言情', value: 'romance' }
      ]},
      { key: 'mood', label: '开篇氛围', type: 'select', placeholder: '选择氛围', required: true, options: [
        { label: '紧张刺激', value: 'tense' },
        { label: '温馨平和', value: 'warm' },
        { label: '神秘悬疑', value: 'mysterious' },
        { label: '幽默轻松', value: 'humorous' }
      ]},
      { key: 'protagonist', label: '主角设定', type: 'textarea', placeholder: '简要描述主角特点', required: true },
      { key: 'scene', label: '开篇场景', type: 'input', placeholder: '开篇场景或环境' }
    ]
  },
  title: {
    title: '爆款书名生成器',
    fields: [
      { key: 'selectedPrompt', label: '提示词模板', type: 'prompt-select', placeholder: '选择提示词模板（可选）', category: 'title' },
      { key: 'count', label: '生成数量', type: 'select', placeholder: '选择生成数量', required: true, options: [
        { label: '5个书名', value: '5' },
        { label: '10个书名', value: '10' },
        { label: '15个书名', value: '15' },
        { label: '20个书名', value: '20' }
      ]},
      { key: 'genre', label: '小说类型', type: 'select', placeholder: '选择类型', required: true, options: [
        { label: '都市', value: 'urban' },
        { label: '玄幻', value: 'fantasy' },
        { label: '科幻', value: 'scifi' },
        { label: '言情', value: 'romance' },
        { label: '悬疑', value: 'mystery' }
      ]},
      { key: 'keywords', label: '关键词', type: 'input', placeholder: '输入相关关键词，用逗号分隔', required: true },
      { key: 'style', label: '风格偏好', type: 'select', placeholder: '选择风格', options: [
        { label: '霸气', value: 'domineering' },
        { label: '文艺', value: 'literary' },
        { label: '悬疑', value: 'mysterious' },
        { label: '简洁', value: 'simple' }
      ]}
    ]
  },
  genre: {
    title: '爆款题材生成器',
    fields: [
      { key: 'selectedPrompt', label: '提示词模板', type: 'prompt-select', placeholder: '选择提示词模板（可选）', category: 'inspiration' },
      { key: 'count', label: '生成数量', type: 'select', placeholder: '选择生成数量', required: true, options: [
        { label: '3个题材', value: '3' },
        { label: '5个题材', value: '5' },
        { label: '8个题材', value: '8' },
        { label: '10个题材', value: '10' }
      ]},
      { key: 'trend', label: '流行趋势', type: 'select', placeholder: '选择趋势', required: true, options: [
        { label: '当前热门', value: 'current' },
        { label: '经典永恒', value: 'classic' },
        { label: '新兴题材', value: 'emerging' }
      ]},
      { key: 'target', label: '目标读者', type: 'select', placeholder: '选择读者群体', required: true, options: [
        { label: '年轻人', value: 'young' },
        { label: '中年人', value: 'middle' },
        { label: '全年龄', value: 'all' }
      ]},
      { key: 'elements', label: '元素偏好', type: 'textarea', placeholder: '希望包含的元素或避免的元素' }
    ]
  },
  brainstorm: {
    title: '脑洞生成器',
    fields: [
      { key: 'selectedPrompt', label: '提示词模板', type: 'prompt-select', placeholder: '选择提示词模板（可选）', category: 'brainstorm' },
      { key: 'count', label: '生成数量', type: 'select', placeholder: '选择生成数量', required: true, options: [
        { label: '3个脑洞', value: '3' },
        { label: '5个脑洞', value: '5' },
        { label: '8个脑洞', value: '8' },
        { label: '10个脑洞', value: '10' }
      ]},
      { key: 'type', label: '脑洞类型', type: 'select', placeholder: '选择类型', required: true, options: [
        { label: '世界设定', value: 'world' },
        { label: '角色设定', value: 'character' },
        { label: '情节转折', value: 'plot' },
        { label: '能力设定', value: 'ability' }
      ]},
      { key: 'creativity', label: '创意程度', type: 'select', placeholder: '选择程度', required: true, options: [
        { label: '常规', value: 'normal' },
        { label: '新颖', value: 'novel' },
        { label: '天马行空', value: 'wild' }
      ]},
      { key: 'base', label: '基础设定', type: 'textarea', placeholder: '现有的设定基础（可选）' }
    ]
  },
  synopsis: {
    title: '简介生成器',
    hasNovelSelector: true, // 标记需要小说选择器
    fields: [
      { key: 'selectedNovel', label: '选择小说', type: 'novel-select', placeholder: '请选择要生成简介的小说', required: true },
      { key: 'selectedChapters', label: '参考章节', type: 'chapter-select', placeholder: '选择要参考的章节（可多选）' },
      { key: 'selectedPrompt', label: '提示词模板', type: 'prompt-select', placeholder: '选择提示词模板（可选）', category: 'content' },
      { key: 'style', label: '简介风格', type: 'select', placeholder: '选择风格', options: [
        { label: '悬疑吸引', value: 'suspense' },
        { label: '直白介绍', value: 'direct' },
        { label: '情感共鸣', value: 'emotional' }
      ]}
    ]
  },
  worldview: {
    title: '宏达世界观生成器',
    hasNovelSelector: true, // 标记需要小说选择器
    fields: [
      { key: 'selectedNovel', label: '选择小说', type: 'novel-select', placeholder: '请选择要生成世界观的小说', required: true },
      { key: 'selectedChapters', label: '参考章节', type: 'chapter-select', placeholder: '选择要参考的章节（可多选）' },
      { key: 'selectedPrompt', label: '提示词模板', type: 'prompt-select', placeholder: '选择提示词模板（可选）', category: 'worldview' },
      { key: 'type', label: '世界类型', type: 'select', placeholder: '选择类型', required: true, options: [
        { label: '奇幻世界', value: 'fantasy' },
        { label: '科幻未来', value: 'scifi' },
        { label: '古代历史', value: 'ancient' },
        { label: '现代都市', value: 'modern' }
      ]},
      { key: 'scale', label: '世界规模', type: 'select', placeholder: '选择规模', required: true, options: [
        { label: '单一城市', value: 'city' },
        { label: '国家大陆', value: 'continent' },
        { label: '多个星球', value: 'planets' },
        { label: '宇宙级别', value: 'universe' }
      ]}
    ]
  },
  character: {
    title: '角色生成器',
    fields: [
      { key: 'selectedPrompt', label: '提示词模板', type: 'prompt-select', placeholder: '选择提示词模板（可选）', category: 'character' },
      { key: 'count', label: '生成数量', type: 'input', placeholder: '输入数量（1-15个角色）', required: true },
      { key: 'role', label: '角色定位', type: 'select', placeholder: '选择定位', required: true, options: [
        { label: '主角', value: 'protagonist' },
        { label: '配角', value: 'supporting' },
        { label: '反派', value: 'antagonist' },
        { label: '路人', value: 'background' }
      ]},
      { key: 'gender', label: '性别', type: 'select', placeholder: '选择性别', options: [
        { label: '男性', value: 'male' },
        { label: '女性', value: 'female' },
        { label: '不限', value: 'any' }
      ]},
      { key: 'personality', label: '性格特点', type: 'textarea', placeholder: '期望的性格特点', required: true }
    ]
  },
  conflict: {
    title: '冲突生成器',
    hasNovelSelector: true, // 标记需要小说选择器
    fields: [
      { key: 'selectedNovel', label: '选择小说', type: 'novel-select', placeholder: '请选择要生成冲突的小说', required: true },
      { key: 'selectedChapters', label: '参考章节', type: 'chapter-select', placeholder: '选择要参考的章节（可多选）' },
      { key: 'selectedPrompt', label: '提示词模板', type: 'prompt-select', placeholder: '选择提示词模板（可选）', category: 'conflict' },
      { key: 'type', label: '冲突类型', type: 'select', placeholder: '选择类型', required: true, options: [
        { label: '人物冲突', value: 'character' },
        { label: '社会冲突', value: 'social' },
        { label: '内心冲突', value: 'internal' },
        { label: '环境冲突', value: 'environment' }
      ]},
      { key: 'intensity', label: '冲突强度', type: 'select', placeholder: '选择强度', required: true, options: [
        { label: '轻微', value: 'mild' },
        { label: '中等', value: 'moderate' },
        { label: '激烈', value: 'intense' }
      ]},
      { key: 'background', label: '背景设定', type: 'textarea', placeholder: '故事背景和现有冲突' }
    ]
  },
  bookSplit: {
    title: '智能一键拆书',
    isSpecialTool: true, // 标记为特殊工具，不使用常规生成流程
    fields: [
      { key: 'file', label: '上传文件', type: 'file', placeholder: '选择要拆分的小说文件', required: true },
      { key: 'mode', label: '拆分模式', type: 'select', placeholder: '选择拆分模式', required: true, options: [
        { label: '智能模式（推荐）', value: 'smart' },
        { label: '手动模式', value: 'manual' },
        { label: '大文件模式', value: 'large' }
      ]},
      { key: 'encoding', label: '文件编码', type: 'select', placeholder: '选择编码', options: [
        { label: 'UTF-8', value: 'utf-8' },
        { label: 'GBK/GB2312', value: 'gbk' }
      ]},
      { key: 'minChapterLength', label: '最小章节长度', type: 'input', placeholder: '最小章节字数（默认500）' }
    ]
  }
}

const currentTool = computed(() => {
  return toolsConfig[currentToolType.value] || {}
})

const currentToolType = ref('')

// 计算属性：检查是否可以生成
const canGenerate = computed(() => {
  if (!currentTool.value.fields) return false
  
  // 检查必填字段
  const requiredFields = currentTool.value.fields.filter(field => field.required)
  return requiredFields.every(field => {
    const value = toolForm[field.key]
    if (!value) return false
    
    // 根据字段类型检查值
    if (field.type === 'novel-select') {
      return value !== null && value !== undefined && value !== ''
    }
    
    if (field.type === 'chapter-select') {
      return Array.isArray(value) && value.length > 0
    }
    
    // 对于字符串类型字段
    if (typeof value === 'string') {
      return value.trim() !== ''
    }
    
    // 对于其他类型
    return Boolean(value)
  })
})

// 计算属性：显示内容（用于流式输出）
const displayContent = computed(() => {
  return generatedContent.value
})

const openTool = (toolType) => {
  currentToolType.value = toolType

  // 所有拆书相关工具都跳转到拆书分析页面
  const bookAnalysisTools = [
    'bookSplit', 'chapterAnalysis', 'writingTechniques',
    'characterAnalysis', 'plotAnalysis'
  ]

  if (bookAnalysisTools.includes(toolType)) {
    window.location.hash = '#/book-analysis'
    return
  }

  // 如果是全自动拆书工具，打开全自动拆书对话框
  if (toolType === 'autoBookSplit') {
    openAutoBookSplitDialog()
    return
  }

  // 其他工具暂时显示提示
  ElMessage.info('该功能正在开发中，敬请期待！')
}

const clearForm = () => {
  Object.keys(toolForm).forEach(key => {
    delete toolForm[key]
  })
  generatedContent.value = ''
  generatingProgress.value = 0
  generatingStatusText.value = ''
  selectedNovelChapters.value = []
  selectedPromptData.value = null
}

const generateContent = async () => {
  if (!canGenerate.value) {
    ElMessage.warning('请填写所有必填字段')
    return
  }
  
  // 特殊验证：角色生成器的数量检查
  if (currentToolType.value === 'character' && toolForm.count) {
    const count = parseInt(toolForm.count)
    if (isNaN(count) || count < 1 || count > 15) {
      ElMessage.warning('角色数量必须是1-15之间的数字')
      return
    }
  }
  
  if (!novelStore.isApiConfigured) {
    ElMessage.error('请先配置API密钥')
    return
  }
  
  generating.value = true
  generatedContent.value = ''
  generatingProgress.value = 0
  generatingStatusText.value = '正在准备生成...'
  
  try {
    // 构建提示词
    const prompt = buildPrompt()
    console.log('工具生成提示词:', prompt)
    
    // 开始进度模拟
    const progressInterval = setInterval(() => {
      if (generatingProgress.value < 90) {
        generatingProgress.value += Math.random() * 10
        updateStatusText()
      }
    }, 500)
    
    // 调用API生成内容（带流式输出）
    const response = await novelStore.generateContent(prompt, (chunk) => {
      // 流式更新内容
      generatedContent.value += chunk
      
      // 自动滚动到底部
      nextTick(() => {
        if (resultTextarea.value) {
          const textarea = resultTextarea.value.$el.querySelector('textarea')
          if (textarea) {
            textarea.scrollTop = textarea.scrollHeight
          }
        }
      })
    })
    
    // 清除进度定时器
    clearInterval(progressInterval)
    generatingProgress.value = 100
    generatingStatusText.value = '生成完成'
    
    if (!response || !response.trim()) {
      throw new Error('AI返回内容为空')
    }
    
    // 确保内容完整
    if (!generatedContent.value) {
      generatedContent.value = response
    }
    
    ElMessage.success('内容生成成功！')
    
  } catch (error) {
    console.error('生成内容失败:', error)
    ElMessage.error('生成失败：' + error.message)
    generatedContent.value = ''
  } finally {
    generating.value = false
    generatingProgress.value = 0
    generatingStatusText.value = ''
  }
}

const updateStatusText = () => {
  const progress = generatingProgress.value
  if (progress < 20) {
    generatingStatusText.value = '正在分析需求...'
  } else if (progress < 40) {
    generatingStatusText.value = '正在构思内容...'
  } else if (progress < 60) {
    generatingStatusText.value = '正在生成内容...'
  } else if (progress < 80) {
    generatingStatusText.value = '正在优化表达...'
  } else {
    generatingStatusText.value = '即将完成...'
  }
}

const buildPrompt = () => {
  const tool = currentTool.value
  let prompt = ''
  let useTemplate = selectedPromptData.value && selectedPromptData.value.content
  
  // 首先构建小说信息部分（如果工具支持小说选择器）
  let novelInfoSection = ''
  if (tool.hasNovelSelector && toolForm.selectedNovel) {
    const selectedNovel = novelList.value.find(novel => novel.value === toolForm.selectedNovel)
    if (selectedNovel) {
      // 获取完整的小说数据
      const originalNovels = JSON.parse(localStorage.getItem('novels') || '[]')
      const originalNovel = originalNovels.find(n => n.id == selectedNovel.value || n.title === selectedNovel.label)
      
      novelInfoSection += `=== 目标小说信息 ===\n`
      novelInfoSection += `小说标题：${selectedNovel.label}\n`
      
      if (originalNovel) {
        // 自动添加小说的基本信息
        if (originalNovel.genre) {
          novelInfoSection += `小说类型：${originalNovel.genre}\n`
        }
        if (originalNovel.description) {
          novelInfoSection += `小说简介：${originalNovel.description}\n`
        }
        if (originalNovel.tags && Array.isArray(originalNovel.tags)) {
          novelInfoSection += `标签：${originalNovel.tags.join('、')}\n`
        }
        
        // 添加角色信息
        if (originalNovel.characters && Array.isArray(originalNovel.characters) && originalNovel.characters.length > 0) {
          novelInfoSection += `\n=== 主要角色 ===\n`
          originalNovel.characters.forEach(char => {
            if (char.name) {
              novelInfoSection += `${char.name}：${char.description || char.personality || '主要角色'}\n`
            }
          })
        }
        
        // 添加世界观设定
        if (originalNovel.worldSettings && Array.isArray(originalNovel.worldSettings) && originalNovel.worldSettings.length > 0) {
          novelInfoSection += `\n=== 世界观设定 ===\n`
          originalNovel.worldSettings.forEach(setting => {
            novelInfoSection += `${setting.name || setting.title}：${setting.description || setting.content}\n`
          })
        }
      }
      
      // 添加选中章节的内容
      if (toolForm.selectedChapters && toolForm.selectedChapters.length > 0) {
        novelInfoSection += `\n=== 参考章节内容 ===\n`
        toolForm.selectedChapters.forEach(chapterId => {
          const chapter = selectedNovelChapters.value.find(ch => ch.value === chapterId)
          if (chapter) {
            novelInfoSection += `\n【${chapter.label}】\n`
            if (chapter.description) {
              novelInfoSection += `大纲：${chapter.description}\n`
            }
            if (chapter.content) {
              // 截取部分内容，避免过长
              const content = chapter.content.length > 500 ? 
                chapter.content.substring(0, 500) + '...' : chapter.content
              novelInfoSection += `内容：${content}\n`
            }
          }
        })
        novelInfoSection += `\n`
      }
    }
  }
  
  // 如果选择了提示词模板，使用模板内容
  if (useTemplate) {
    prompt = selectedPromptData.value.content
    
    // 如果有小说信息，首先添加到提示词前面
    if (novelInfoSection) {
      prompt = novelInfoSection + '\n' + prompt
    }
    
    // 替换模板中的变量
    if (tool.hasNovelSelector && toolForm.selectedNovel) {
      const selectedNovel = novelList.value.find(novel => novel.value === toolForm.selectedNovel)
      if (selectedNovel) {
        const originalNovels = JSON.parse(localStorage.getItem('novels') || '[]')
        const originalNovel = originalNovels.find(n => n.id == selectedNovel.value || n.title === selectedNovel.label)
        
        // 替换小说相关变量
        prompt = prompt.replace(/\{小说标题\}/g, selectedNovel.label)
        if (originalNovel) {
          prompt = prompt.replace(/\{小说类型\}/g, originalNovel.genre || '未设定')
          prompt = prompt.replace(/\{小说简介\}/g, originalNovel.description || '无简介')
          prompt = prompt.replace(/\{标签\}/g, originalNovel.tags ? originalNovel.tags.join('、') : '无标签')
          
          // 替换角色信息
          if (originalNovel.characters && Array.isArray(originalNovel.characters) && originalNovel.characters.length > 0) {
            const charactersInfo = originalNovel.characters.map(char => 
              `${char.name}：${char.description || char.personality || '主要角色'}`
            ).join('\n')
            prompt = prompt.replace(/\{主要人物\}/g, charactersInfo)
          } else {
            prompt = prompt.replace(/\{主要人物\}/g, '暂无详细人物设定')
          }
          
          // 替换世界观信息
          if (originalNovel.worldSettings && Array.isArray(originalNovel.worldSettings) && originalNovel.worldSettings.length > 0) {
            const worldInfo = originalNovel.worldSettings.map(setting => 
              `${setting.name || setting.title}：${setting.description || setting.content}`
            ).join('\n')
            prompt = prompt.replace(/\{世界观设定\}/g, worldInfo)
          } else {
            prompt = prompt.replace(/\{世界观设定\}/g, '暂无详细世界观设定')
          }
        }
        
        // 替换章节信息
        if (toolForm.selectedChapters && toolForm.selectedChapters.length > 0) {
          let chaptersInfo = ''
          toolForm.selectedChapters.forEach(chapterId => {
            const chapter = selectedNovelChapters.value.find(ch => ch.value === chapterId)
            if (chapter) {
              chaptersInfo += `\n【${chapter.label}】\n`
              if (chapter.description) {
                chaptersInfo += `大纲：${chapter.description}\n`
              }
              if (chapter.content) {
                const content = chapter.content.length > 500 ? 
                  chapter.content.substring(0, 500) + '...' : chapter.content
                chaptersInfo += `内容：${content}\n`
              }
            }
          })
          prompt = prompt.replace(/\{参考章节内容\}/g, chaptersInfo || '暂无参考章节')
        } else {
          prompt = prompt.replace(/\{参考章节内容\}/g, '暂无参考章节')
        }
      }
    }
    
    // 替换表单字段变量
    tool.fields.forEach(field => {
      if (field.type !== 'novel-select' && field.type !== 'chapter-select' && field.type !== 'prompt-select' && toolForm[field.key]) {
        const value = toolForm[field.key]
        // 支持中文和英文字段名
        const patterns = [
          new RegExp(`\\{${field.label}\\}`, 'g'),
          new RegExp(`\\{${field.key}\\}`, 'g')
        ]
        patterns.forEach(pattern => {
          prompt = prompt.replace(pattern, value)
        })
      }
    })
    
    // 特殊处理生成数量变量
    if (toolForm.count) {
      prompt = prompt.replace(/\{生成数量\}/g, toolForm.count)
      prompt = prompt.replace(/\{count\}/g, toolForm.count)
    }
    
    // 清理未替换的变量
    prompt = prompt.replace(/\{[^}]*\}/g, '[待填充]')
    
    console.log('使用提示词模板构建的提示词:', prompt)
    return prompt
  }
  
  // 如果没有选择提示词模板，使用默认构建方式
  prompt = `请作为一个专业的${tool.title}，根据以下信息生成高质量的内容：\n\n`
  
  // 添加小说信息
  if (novelInfoSection) {
    prompt += novelInfoSection
  }
  
  // 添加其他字段信息
  tool.fields.forEach(field => {
    // 跳过小说、章节选择和提示词选择字段
    if (field.type !== 'novel-select' && field.type !== 'chapter-select' && field.type !== 'prompt-select' && toolForm[field.key]) {
      prompt += `${field.label}：${toolForm[field.key]}\n`
    }
  })
  
  // 根据不同工具类型添加具体要求
  switch (currentToolType.value) {
    case 'outline':
      prompt += '\n请根据上述小说信息生成详细的章节细纲，包括：\n1. 每章的标题和主要情节\n2. 故事发展脉络和转折点\n3. 人物关系变化\n4. 冲突设置和解决\n5. 整体结构要完整（开头、发展、高潮、结局）\n6. 与已有角色和世界观保持一致\n\n请按照以下格式输出：\n第一章：章节标题\n- 主要情节描述\n- 重要转折点\n第二章：...'
      break
    case 'cheat':
      prompt += '\n请根据上述小说的类型和世界观，生成一个独特的金手指设定，包括：\n1. 能力名称和核心功能\n2. 详细的能力描述和效果\n3. 获得方式和触发条件\n4. 使用限制和副作用\n5. 能力的成长路径和进阶可能\n6. 与故事情节和世界观的结合点\n\n要求创意新颖，符合小说类型的特点，与现有角色和设定协调。'
      break
    case 'opening':
      prompt += '\n请生成一个引人入胜的小说开篇，要求：\n1. 字数控制在500-800字\n2. 立即抓住读者注意力\n3. 巧妙引入主角和背景\n4. 设置悬念或冲突点\n5. 语言风格符合所选氛围\n6. 为后续情节发展做好铺垫\n\n请直接输出开篇内容，无需其他说明。'
      break
    case 'title':
      const titleCount = toolForm.count || '10'
      prompt += `\n请生成${titleCount}个不同风格的书名供选择，要求：\n1. 符合所选类型的特点\n2. 体现关键词元素\n3. 具有吸引力和记忆点\n4. 长度适中（3-8个字为佳）\n5. 避免俗套，有创新性\n6. 风格多样化，覆盖不同类型\n\n请按照以下格式输出：\n1. 书名 - 创意说明\n2. 书名 - 创意说明\n3. 书名 - 创意说明\n...\n${titleCount}. 书名 - 创意说明`
      break
    case 'genre':
      const genreCount = toolForm.count || '5'
      prompt += `\n请分析当前流行趋势，提供${genreCount}个具有潜力的题材方向，每个题材都要包括：\n1. 题材名称和核心概念\n2. 市场潜力分析\n3. 目标读者群体\n4. 创作要点和注意事项\n5. 成功案例参考\n6. 创新突破点建议\n\n请按照以下格式输出：\n=== 题材1 ===\n名称：[题材名称]\n核心概念：[详细描述]\n市场潜力：[分析]\n目标读者：[读者群体]\n创作要点：[注意事项]\n成功案例：[参考作品]\n创新点：[突破建议]\n\n=== 题材2 ===\n...\n\n以此类推到第${genreCount}个题材。`
      break
    case 'brainstorm':
      const brainstormCount = toolForm.count || '5'
      prompt += `\n请提供${brainstormCount}个创意脑洞，每个都要：\n1. 独特有趣，避免俗套\n2. 具有可扩展性\n3. 符合所选创意程度\n4. 包含具体的设定细节\n5. 提供发展方向建议\n\n请按照以下格式输出：\n脑洞1：标题\n- 核心设定\n- 创意亮点\n- 发展方向\n脑洞2：标题\n- 核心设定\n- 创意亮点\n- 发展方向\n\n以此类推，直到第${brainstormCount}个脑洞...`
      break
    case 'synopsis':
      prompt += '\n请根据上述小说信息生成吸引人的简介，要求：\n1. 突出故事亮点和悬念\n2. 介绍主角和核心冲突\n3. 体现故事的独特性\n4. 语言精炼有力\n5. 长度控制在100-200字\n6. 符合所选风格特点\n7. 与现有角色和世界观保持一致\n\n请直接输出简介内容。'
      break
    case 'worldview':
      prompt += '\n请根据上述小说信息，扩展和完善世界观设定，包括：\n1. 世界的基本架构和地理环境\n2. 社会制度和政治结构\n3. 文化传统和价值观念\n4. 科技水平或魔法系统\n5. 历史背景和重要事件\n6. 独特的规则和法则\n7. 与现有故事情节和角色的结合点\n\n要求设定合理，富有创意，具有内在逻辑性，与现有设定协调。'
      break
    case 'character':
      const characterCount = parseInt(toolForm.count || '1')
      if (characterCount === 1) {
        prompt += '\n请生成详细的角色档案，包括：\n1. 基本信息（姓名、年龄、职业等）\n2. 外貌特征和穿着风格\n3. 性格特点和行为习惯\n4. 背景故事和成长经历\n5. 能力特长和弱点\n6. 人际关系和社会地位\n7. 内心动机和目标追求\n8. 与主线情节的关系\n\n要求人物立体丰满，符合角色定位。'
      } else {
        prompt += `\n请生成${characterCount}个详细的角色档案，每个角色都要包括：\n1. 基本信息（姓名、年龄、职业等）\n2. 外貌特征和穿着风格\n3. 性格特点和行为习惯\n4. 背景故事和成长经历\n5. 能力特长和弱点\n6. 人际关系和社会地位\n7. 内心动机和目标追求\n8. 与主线情节的关系\n\n要求：\n- 每个人物都要立体丰满，符合角色定位\n- 角色之间要有差异化，避免重复\n- 可以设计角色间的关系和互动\n- 如果生成的是同一类型角色，请在性格、背景、能力等方面做出明显区分\n- 按照以下格式输出：\n\n=== 角色1 ===\n姓名：[角色姓名]\n[详细信息]\n\n=== 角色2 ===\n姓名：[角色姓名]\n[详细信息]\n\n以此类推到第${characterCount}个角色...`
      }
      break
    case 'conflict':
      prompt += '\n请根据上述小说信息设计合理的冲突情节，包括：\n1. 冲突的起因和背景\n2. 冲突各方的立场和动机\n3. 冲突的发展过程和升级\n4. 关键转折点和高潮设计\n5. 可能的解决方向和结果\n6. 对现有角色成长的影响\n7. 与整体故事和世界观的呼应\n\n要求冲突合理有力，推动情节发展，与现有设定协调。'
      break
  }
  
      console.log('最终构建的提示词:', prompt)
  console.log('小说信息部分:', novelInfoSection)
  console.log('是否使用模板:', useTemplate)
  console.log('选中的小说:', toolForm.selectedNovel)
  console.log('选中的章节:', toolForm.selectedChapters)
  return prompt
}

const copyToClipboard = async () => {
  if (!generatedContent.value) {
    ElMessage.warning('没有可复制的内容')
    return
  }
  
  try {
    await navigator.clipboard.writeText(generatedContent.value)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    // 如果 Clipboard API 不可用，使用传统方法
    const textArea = document.createElement('textarea')
    textArea.value = generatedContent.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('内容已复制到剪贴板')
  }
}

const saveResult = () => {
  if (!generatedContent.value) {
    ElMessage.warning('没有可保存的内容')
    return
  }
  
  try {
    // 创建文件内容
    const content = `=== ${currentTool.value.title} ===
生成时间：${new Date().toLocaleString()}

=== 生成参数 ===
${currentTool.value.fields.map(field => 
  toolForm[field.key] ? `${field.label}：${toolForm[field.key]}` : ''
).filter(line => line).join('\n')}

=== 生成结果 ===
${generatedContent.value}
`
    
    // 创建并下载文件
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${currentTool.value.title}_${new Date().getTime()}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('结果已保存到本地文件')
  } catch (error) {
    console.error('保存文件失败:', error)
    ElMessage.error('保存失败')
  }
}

// 全自动拆书相关方法
const openAutoBookSplitDialog = () => {
  showAutoSplitDialog.value = true
  resetAutoSplit()
}

const handleAutoSplitFile = async (file) => {
  try {
    isAutoSplitting.value = true
    autoSplitProgress.value = 0
    autoSplitStatus.value = '开始处理文件...'

    await performAutoSplit(file.raw)

  } catch (error) {
    console.error('全自动拆书失败:', error)
    ElMessage.error(`全自动拆书失败: ${error.message}`)
    isAutoSplitting.value = false
  }
}

const performAutoSplit = async (file) => {
  const startTime = Date.now()

  try {
    // 第1步：解析文件内容
    autoSplitStatus.value = '正在解析文件内容...'
    autoSplitProgress.value = 5

    const fileContent = await readFileContent(file)
    if (!fileContent || fileContent.length < 100) {
      throw new Error('文件内容为空或过短，请检查文件格式')
    }

    // 第2步：整体结构分析
    autoSplitStatus.value = '正在分析整体结构...'
    autoSplitProgress.value = 15

    const structurePrompt = `请深度分析这本书的整体结构和写作框架：

【分析要求】
1. 故事结构：开头-发展-高潮-结局的具体安排
2. 章节布局：章节如何划分，每章的功能和作用
3. 叙事节奏：快慢节奏的控制技巧
4. 情节线索：主线、副线的交织方法
5. 结构创新：有什么独特的结构设计

【文本内容】
${fileContent.slice(0, 8000)}${fileContent.length > 8000 ? '\n...(已截取前8000字符，请基于此分析整体结构)' : ''}`

    const structureAnalysis = await novelStore.generateContentWithAPI('', '', structurePrompt, 2500)

    // 第3步：写作技巧拆解
    autoSplitStatus.value = '正在拆解写作技巧...'
    autoSplitProgress.value = 30

    const techniquesPrompt = `请深度拆解这本书的写作技巧和方法：

【拆解重点】
1. 开头技巧：如何抓住读者注意力
2. 悬念设置：如何制造和维持悬念
3. 冲突构建：如何设计和升级冲突
4. 情感渲染：如何调动读者情感
5. 细节描写：如何运用细节增强真实感
6. 对话技巧：如何通过对话推进情节和塑造人物
7. 转场技巧：如何自然地切换场景和时间
8. 结尾技巧：如何设计令人满意的结局

【文本内容】
${fileContent.slice(2000, 10000)}${fileContent.length > 10000 ? '\n...(已截取中间部分进行技巧分析)' : ''}`

    const techniquesAnalysis = await novelStore.generateContentWithAPI('', '', techniquesPrompt, 3000)

    // 第4步：人物塑造方法
    autoSplitStatus.value = '正在分析人物塑造方法...'
    autoSplitProgress.value = 45

    const characterPrompt = `请分析这本书的人物塑造方法和技巧：

【分析维度】
1. 人物设定：如何设计人物的基本属性和背景
2. 性格塑造：如何通过行为、对话、心理描写展现性格
3. 人物弧光：主要人物如何成长和变化
4. 关系网络：人物之间的关系如何设计和发展
5. 个性化语言：如何让每个人物有独特的说话方式
6. 矛盾冲突：人物内心和人物之间的矛盾如何设计

【文本内容】
${fileContent.slice(1000, 9000)}${fileContent.length > 9000 ? '\n...(已截取相关部分进行人物分析)' : ''}`

    const characterAnalysis = await novelStore.generateContentWithAPI('', '', characterPrompt, 2500)

    // 第5步：语言风格分析
    autoSplitStatus.value = '正在分析语言风格...'
    autoSplitProgress.value = 60

    const languagePrompt = `请深度分析这本书的语言风格和用词特色：

【分析重点】
1. 词汇选择：作者偏好使用什么类型的词汇（文雅、通俗、专业等）
2. 句式特点：长句短句的搭配，复杂句式的运用
3. 修辞手法：比喻、拟人、排比等修辞技巧的使用
4. 语言节奏：语言的快慢节奏如何配合情节发展
5. 方言俚语：是否使用方言、俚语增加真实感
6. 情感色彩：如何通过用词传达情感和氛围
7. 独特表达：作者有什么独特的表达方式和语言习惯

【文本内容】
${fileContent.slice(3000, 11000)}${fileContent.length > 11000 ? '\n...(已截取相关部分进行语言分析)' : ''}`

    const languageAnalysis = await novelStore.generateContentWithAPI('', '', languagePrompt, 2500)

    // 第6步：情节设计拆解
    autoSplitStatus.value = '正在拆解情节设计...'
    autoSplitProgress.value = 75

    const plotPrompt = `请拆解这本书的情节设计方法和技巧：

【拆解要点】
1. 情节线索：主线情节如何设计和推进
2. 副线编织：副线情节如何与主线交织
3. 转折设计：重要转折点如何设置和铺垫
4. 高潮构建：如何一步步推向高潮
5. 伏笔呼应：前后伏笔如何设置和呼应
6. 意外设计：如何设计合理的意外和反转
7. 节奏控制：紧张和缓解的节奏如何把握

【文本内容】
${fileContent.slice(4000, 12000)}${fileContent.length > 12000 ? '\n...(已截取相关部分进行情节分析)' : ''}`

    const plotAnalysis = await novelStore.generateContentWithAPI('', '', plotPrompt, 2500)

    // 第7步：写作亮点提取
    autoSplitStatus.value = '正在提取写作亮点...'
    autoSplitProgress.value = 85

    const highlightsPrompt = `请提取这本书最值得学习的写作亮点和精华：

【提取重点】
1. 创新技巧：作者使用了哪些创新的写作技巧
2. 精彩片段：最精彩的段落和其精彩之处
3. 巧妙设计：情节、人物、结构上的巧妙设计
4. 情感共鸣：如何引起读者强烈的情感共鸣
5. 深度思考：作品传达的深层思想和哲理
6. 学习价值：对写作者最有学习价值的地方
7. 可复用技巧：可以应用到其他作品的技巧

【文本内容】
${fileContent.slice(0, 15000)}${fileContent.length > 15000 ? '\n...(已截取前15000字符进行亮点提取)' : ''}`

    const highlightsAnalysis = await novelStore.generateContentWithAPI('', '', highlightsPrompt, 3000)

    // 第8步：整理和保存结果
    autoSplitStatus.value = '正在整理拆书结果...'
    autoSplitProgress.value = 95

    // 第9步：完成拆书分析
    autoSplitStatus.value = '全自动拆书分析完成！'
    autoSplitProgress.value = 100

    const processingTime = Math.round((Date.now() - startTime) / 1000)

    // 生成完整的拆书分析结果
    autoSplitResult.value = {
      // 基本信息
      fileName: file.name,
      totalWords: fileContent.length,
      processingTime: `${processingTime}秒`,
      novelId: Date.now().toString(),

      // 完整的拆书分析结果
      analysisResults: {
        structureAnalysis: structureAnalysis,      // 整体结构分析
        techniquesAnalysis: techniquesAnalysis,    // 写作技巧拆解
        characterAnalysis: characterAnalysis,      // 人物塑造方法
        languageAnalysis: languageAnalysis,        // 语言风格分析
        plotAnalysis: plotAnalysis,                // 情节设计拆解
        highlightsAnalysis: highlightsAnalysis     // 写作亮点提取
      },

      // 原文内容
      fileContent: fileContent,

      // 分析概要
      summary: `已完成对《${file.name}》的全面拆书分析，包括：
• 整体结构和写作框架分析
• 写作技巧和方法拆解
• 人物塑造技巧分析
• 语言风格和用词特色
• 情节设计方法拆解
• 写作亮点和精华提取

总计${fileContent.length.toLocaleString()}字，耗时${processingTime}秒完成深度拆书分析。`
    }

    isAutoSplitting.value = false
    ElMessage.success(`全自动拆书完成！识别${totalChapters}个章节，共${fileContent.length.toLocaleString()}字`)

  } catch (error) {
    console.error('全自动拆书失败:', error)
    autoSplitStatus.value = '分析失败'
    isAutoSplitting.value = false
    throw error
  }
}

// 读取文件内容的辅助函数
const readFileContent = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        let content = e.target.result

        // 处理不同编码
        if (content.includes('�')) {
          // 可能是编码问题，尝试重新读取
          const reader2 = new FileReader()
          reader2.onload = (e2) => {
            resolve(e2.target.result)
          }
          reader2.onerror = reject
          reader2.readAsText(file, 'gbk')
        } else {
          resolve(content)
        }
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = reject
    reader.readAsText(file, 'utf-8')
  })
}

const viewAutoSplitResult = () => {
  if (autoSplitResult.value) {
    // 显示详细的拆书分析结果
    showAutoSplitDialog.value = false

    // 创建详细的拆书结果展示
    const results = autoSplitResult.value.analysisResults

    // 使用Element Plus的消息框显示概要
    ElMessage({
      message: '拆书分析完成！正在跳转到详细结果页面...',
      type: 'success',
      duration: 3000
    })

    // 将拆书结果保存到localStorage，供其他页面使用
    localStorage.setItem('latestBookAnalysis', JSON.stringify({
      fileName: autoSplitResult.value.fileName,
      timestamp: new Date().toLocaleString(),
      summary: autoSplitResult.value.summary,
      results: {
        structureAnalysis: results.structureAnalysis,
        techniquesAnalysis: results.techniquesAnalysis,
        characterAnalysis: results.characterAnalysis,
        languageAnalysis: results.languageAnalysis,
        plotAnalysis: results.plotAnalysis,
        highlightsAnalysis: results.highlightsAnalysis
      },
      fileContent: autoSplitResult.value.fileContent,
      processingTime: autoSplitResult.value.processingTime,
      totalWords: autoSplitResult.value.totalWords
    }))

    // 跳转到拆书工具页面查看详细结果
    window.location.hash = '#/book-analysis'
  }
}

const resetAutoSplit = () => {
  autoSplitProgress.value = 0
  autoSplitStatus.value = ''
  autoSplitResult.value = null
  isAutoSplitting.value = false
}

// 加载提示词数据
const loadPrompts = () => {
  try {
    const savedPrompts = localStorage.getItem('prompts')
    if (savedPrompts) {
      availablePrompts.value = JSON.parse(savedPrompts)
    } else {
      availablePrompts.value = []
    }
    console.log('加载提示词数据:', availablePrompts.value.length)
  } catch (error) {
    console.error('加载提示词失败:', error)
    availablePrompts.value = []
  }
}

// 根据分类获取提示词
const getPromptsByCategory = (category) => {
  if (!category) return []
  return availablePrompts.value.filter(prompt => prompt.category === category)
}

// 当选择提示词时
const onPromptChange = (promptId) => {
  console.log('选择的提示词ID:', promptId)
  if (promptId) {
    selectedPromptData.value = availablePrompts.value.find(prompt => prompt.id === promptId)
    console.log('选择的提示词数据:', selectedPromptData.value)
  } else {
    selectedPromptData.value = null
  }
}

// 验证角色数量
const isValidCharacterCount = (count) => {
  const num = parseInt(count)
  return !isNaN(num) && num >= 1 && num <= 15
}

// 角色数量输入验证
const validateCharacterCount = (field, value) => {
  if (field.key === 'count' && currentToolType.value === 'character') {
    // 限制只能输入数字
    const numericValue = value.replace(/[^0-9]/g, '')
    if (numericValue !== value) {
      toolForm[field.key] = numericValue
    }
  }
}

// 文件上传处理
const handleFileChange = (file) => {
  selectedFile.value = file.raw
  toolForm.file = file.raw
  ElMessage.success(`已选择文件：${file.name}`)
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
  toolForm.file = null
  ElMessage.info('已移除文件')
}

// 组件挂载时加载小说列表和提示词
onMounted(() => {
  loadNovelList()
  loadPrompts()
})
</script>

<style scoped>
.tools-library {
  max-width: 1200px;
  margin: 0 auto;
}


.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.tool-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
}

.tool-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.tool-icon {
  font-size: 48px;
  text-align: center;
  margin-bottom: 16px;
}

.tool-card h3 {
  font-size: 18px;
  color: #2c3e50;
  margin-bottom: 8px;
  text-align: center;
}

.tool-card p {
  font-size: 14px;
  color: #7f8c8d;
  text-align: center;
  line-height: 1.5;
}

.tool-dialog .tool-content {
  padding: 20px 0;
}

.tool-form {
  margin-bottom: 24px;
}

.tool-actions {
  text-align: center;
  margin-bottom: 24px;
}

.tool-actions .el-button {
  margin: 0 8px;
}

.generating-status {
  margin: 16px 0;
  text-align: center;
}

.status-text {
  display: block;
  margin-top: 8px;
  color: #606266;
  font-size: 14px;
}

.tool-result {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.tool-result h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #2c3e50;
}

.result-content-wrapper {
  margin-bottom: 16px;
}

.result-textarea {
  width: 100%;
}

.result-textarea :deep(.el-textarea__inner) {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  resize: vertical;
}

.result-actions {
  text-align: center;
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.result-actions .el-button {
  margin: 4px;
}

/* 小说和章节选择器样式 */
.tool-form .el-select {
  width: 100%;
}

.tool-form .el-select .el-tag {
  max-width: 120px;
}

.tool-form .el-form-item {
  margin-bottom: 18px;
}

/* 提示词选择器样式 */
.prompt-option {
  padding: 8px 0;
}

.prompt-option-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.prompt-option-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* 角色数量提示样式 */
.character-count-hint {
  margin-top: 5px;
  font-size: 12px;
}

.valid-hint {
  color: #67c23a;
}

.invalid-hint {
  color: #f56c6c;
}

/* 文件上传样式 */
.upload-demo {
  width: 100%;
}

.upload-demo .el-upload-dragger {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s;
}

.upload-demo .el-upload-dragger:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.selected-file {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
  font-size: 14px;
}

.selected-file .file-size {
  color: #909399;
  font-size: 12px;
}

.selected-file .remove-btn {
  margin-left: auto;
  color: #f56c6c;
}

@media (max-width: 768px) {
  .tools-grid {
    grid-template-columns: 1fr;
  }
  
  .tool-dialog {
    width: 95% !important;
  }
  
  .result-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .result-actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}

/* 全自动拆书样式 */
.auto-split-content {
  padding: 20px 0;
}

.upload-section {
  text-align: center;
}

.section-header {
  margin-bottom: 30px;
}

.section-header h3 {
  margin: 0 0 10px 0;
  color: #409eff;
  font-size: 20px;
}

.section-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.auto-upload {
  margin: 30px 0;
}

.auto-upload .el-upload-dragger {
  width: 100%;
  height: 150px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s;
}

.auto-upload .el-upload-dragger:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.auto-features {
  text-align: left;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.auto-features h4 {
  margin: 0 0 15px 0;
  color: #409eff;
}

.auto-features ul {
  margin: 0;
  padding-left: 20px;
}

.auto-features li {
  margin: 8px 0;
  color: #606266;
}

.processing-section {
  text-align: center;
  padding: 20px 0;
}

.progress-header {
  margin-bottom: 30px;
}

.progress-header h3 {
  margin: 0 0 10px 0;
  color: #67c23a;
  font-size: 20px;
}

.progress-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.processing-steps {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  padding: 0 20px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.3;
  transition: all 0.3s;
}

.step.active {
  opacity: 1;
}

.step-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.step-text {
  font-size: 12px;
  color: #606266;
}

.result-section {
  text-align: center;
  padding: 20px 0;
}

.result-header h3 {
  margin: 0 0 30px 0;
  color: #67c23a;
  font-size: 20px;
}

.result-stats {
  display: flex;
  justify-content: space-around;
  margin: 30px 0;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.stat-icon {
  font-size: 32px;
}

.stat-content {
  text-align: left;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.result-actions {
  margin-top: 30px;
  display: flex;
  gap: 15px;
  justify-content: center;
}

@media (max-width: 768px) {
  .processing-steps {
    flex-wrap: wrap;
    gap: 20px;
  }

  .result-stats {
    flex-direction: column;
    gap: 15px;
  }

  .stat-card {
    justify-content: center;
  }
}
</style>