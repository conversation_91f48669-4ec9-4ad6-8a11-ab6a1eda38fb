<template>
  <div class="processing-monitor">
    <!-- 总体进度卡片 -->
    <el-card class="progress-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-icon class="header-icon" :class="{ 'rotating': isProcessing }">
              <component :is="statusIcon" />
            </el-icon>
            <div class="header-text">
              <h3>{{ statusTitle }}</h3>
              <p>{{ statusDescription }}</p>
            </div>
          </div>
          <div class="header-right">
            <el-tag :type="statusTagType" size="large">
              {{ statusText }}
            </el-tag>
          </div>
        </div>
      </template>

      <!-- 总体进度条 -->
      <div class="overall-progress">
        <div class="progress-info">
          <span class="progress-label">总体进度</span>
          <span class="progress-value">{{ currentStep }}/{{ totalSteps }}</span>
          <span class="progress-percent">{{ Math.round(overallProgress) }}%</span>
        </div>
        <el-progress
          :percentage="overallProgress"
          :stroke-width="12"
          :show-text="false"
          :status="progressStatus"
          class="main-progress"
        />
        <div class="current-step">
          <el-icon><Clock /></el-icon>
          <span>{{ currentStepText }}</span>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.processedSegments }}/{{ stats.totalSegments }}</div>
            <div class="stat-label">已处理分段</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">
            <el-icon><Reading /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.detectedChapters }}</div>
            <div class="stat-label">检测章节</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatTime(stats.elapsedTime) }}</div>
            <div class="stat-label">已用时间</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.processingSpeed }}</div>
            <div class="stat-label">处理速度</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 分段处理详情 -->
    <el-card class="segments-card" shadow="hover" v-if="segments.length > 0">
      <template #header>
        <div class="segments-header">
          <h4>分段处理详情</h4>
          <el-button-group size="small">
            <el-button 
              :type="viewMode === 'grid' ? 'primary' : ''" 
              @click="viewMode = 'grid'"
            >
              <el-icon><Grid /></el-icon>
              网格
            </el-button>
            <el-button 
              :type="viewMode === 'list' ? 'primary' : ''" 
              @click="viewMode = 'list'"
            >
              <el-icon><List /></el-icon>
              列表
            </el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="segments-grid">
        <div
          v-for="(segment, index) in segments"
          :key="index"
          class="segment-card"
          :class="getSegmentClass(segment.status)"
        >
          <div class="segment-header">
            <span class="segment-title">第 {{ index + 1 }} 段</span>
            <el-icon class="segment-status-icon">
              <component :is="getSegmentIcon(segment.status)" />
            </el-icon>
          </div>
          <div class="segment-content">
            <div class="segment-info">
              <span class="info-item">
                <el-icon><Document /></el-icon>
                {{ segment.size.toLocaleString() }} 字
              </span>
              <span class="info-item" v-if="segment.chapters">
                <el-icon><Reading /></el-icon>
                {{ segment.chapters }} 章节
              </span>
            </div>
            <div class="segment-progress" v-if="segment.status === 'processing'">
              <el-progress
                :percentage="segment.progress || 0"
                :stroke-width="4"
                :show-text="false"
                status="success"
              />
            </div>
            <div class="segment-status-text">
              {{ getSegmentStatusText(segment.status) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="segments-list">
        <div
          v-for="(segment, index) in segments"
          :key="index"
          class="segment-item"
          :class="getSegmentClass(segment.status)"
        >
          <div class="segment-info">
            <div class="segment-main">
              <span class="segment-title">第 {{ index + 1 }} 段</span>
              <span class="segment-size">{{ segment.size.toLocaleString() }} 字</span>
            </div>
            <div class="segment-details">
              <span v-if="segment.chapters" class="detail-item">
                检测到 {{ segment.chapters }} 个章节
              </span>
              <span v-if="segment.processingTime" class="detail-item">
                用时 {{ segment.processingTime }}
              </span>
            </div>
          </div>
          <div class="segment-status">
            <el-icon class="status-icon" :class="getSegmentClass(segment.status)">
              <component :is="getSegmentIcon(segment.status)" />
            </el-icon>
            <span class="status-text">{{ getSegmentStatusText(segment.status) }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 处理日志 -->
    <el-card class="logs-card" shadow="hover" v-if="logs.length > 0">
      <template #header>
        <div class="logs-header">
          <h4>处理日志</h4>
          <div class="log-controls">
            <el-button-group size="small">
              <el-button 
                :type="logFilter === 'all' ? 'primary' : ''" 
                @click="logFilter = 'all'"
              >
                全部
              </el-button>
              <el-button 
                :type="logFilter === 'info' ? 'primary' : ''" 
                @click="logFilter = 'info'"
              >
                信息
              </el-button>
              <el-button 
                :type="logFilter === 'success' ? 'primary' : ''" 
                @click="logFilter = 'success'"
              >
                成功
              </el-button>
              <el-button 
                :type="logFilter === 'warning' ? 'primary' : ''" 
                @click="logFilter = 'warning'"
              >
                警告
              </el-button>
              <el-button 
                :type="logFilter === 'error' ? 'primary' : ''" 
                @click="logFilter = 'error'"
              >
                错误
              </el-button>
            </el-button-group>
            <el-button size="small" @click="clearLogs">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </div>
        </div>
      </template>

      <div class="logs-container">
        <div
          v-for="(log, index) in filteredLogs"
          :key="index"
          class="log-item"
          :class="log.type"
        >
          <div class="log-time">{{ formatLogTime(log.timestamp) }}</div>
          <div class="log-icon">
            <el-icon>
              <component :is="getLogIcon(log.type)" />
            </el-icon>
          </div>
          <div class="log-message">{{ log.message }}</div>
        </div>
      </div>
    </el-card>

    <!-- 错误处理 -->
    <el-card class="error-card" shadow="hover" v-if="hasErrors">
      <template #header>
        <div class="error-header">
          <el-icon class="error-icon"><Warning /></el-icon>
          <h4>处理异常</h4>
        </div>
      </template>

      <div class="error-list">
        <div
          v-for="(error, index) in errors"
          :key="index"
          class="error-item"
        >
          <div class="error-info">
            <span class="error-segment">第 {{ error.segmentIndex + 1 }} 段</span>
            <span class="error-message">{{ error.message }}</span>
          </div>
          <div class="error-actions">
            <el-button size="small" type="primary" @click="retrySegment(error.segmentIndex)">
              重试
            </el-button>
            <el-button size="small" @click="skipSegment(error.segmentIndex)">
              跳过
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import {
  Loading, Check, Close, Warning, Clock, Document, Reading, Timer, TrendCharts,
  Grid, List, Delete, InfoFilled, SuccessFilled, WarningFilled, CircleCloseFilled
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  isProcessing: {
    type: Boolean,
    default: false
  },
  currentStep: {
    type: Number,
    default: 0
  },
  totalSteps: {
    type: Number,
    default: 0
  },
  currentStepText: {
    type: String,
    default: ''
  },
  segments: {
    type: Array,
    default: () => []
  },
  logs: {
    type: Array,
    default: () => []
  },
  errors: {
    type: Array,
    default: () => []
  },
  stats: {
    type: Object,
    default: () => ({
      processedSegments: 0,
      totalSegments: 0,
      detectedChapters: 0,
      elapsedTime: 0,
      processingSpeed: '0 字/秒'
    })
  }
})

// Emits
const emit = defineEmits(['retry-segment', 'skip-segment', 'clear-logs'])

// 响应式数据
const viewMode = ref('grid')
const logFilter = ref('all')

// 计算属性
const overallProgress = computed(() => {
  if (props.totalSteps === 0) return 0
  return (props.currentStep / props.totalSteps) * 100
})

const statusIcon = computed(() => {
  if (props.isProcessing) return Loading
  if (hasErrors.value) return Warning
  if (props.currentStep === props.totalSteps && props.totalSteps > 0) return Check
  return Clock
})

const statusTitle = computed(() => {
  if (props.isProcessing) return '正在处理中...'
  if (hasErrors.value) return '处理遇到问题'
  if (props.currentStep === props.totalSteps && props.totalSteps > 0) return '处理完成'
  return '准备就绪'
})

const statusDescription = computed(() => {
  if (props.isProcessing) return '正在智能分析和处理您的文件'
  if (hasErrors.value) return '部分分段处理失败，请查看错误详情'
  if (props.currentStep === props.totalSteps && props.totalSteps > 0) return '所有分段处理完成'
  return '等待开始处理'
})

const statusText = computed(() => {
  if (props.isProcessing) return '处理中'
  if (hasErrors.value) return '有错误'
  if (props.currentStep === props.totalSteps && props.totalSteps > 0) return '已完成'
  return '就绪'
})

const statusTagType = computed(() => {
  if (props.isProcessing) return 'primary'
  if (hasErrors.value) return 'danger'
  if (props.currentStep === props.totalSteps && props.totalSteps > 0) return 'success'
  return 'info'
})

const progressStatus = computed(() => {
  if (hasErrors.value) return 'exception'
  if (props.currentStep === props.totalSteps && props.totalSteps > 0) return 'success'
  return undefined
})

const hasErrors = computed(() => {
  return props.errors && props.errors.length > 0
})

const filteredLogs = computed(() => {
  if (logFilter.value === 'all') return props.logs
  return props.logs.filter(log => log.type === logFilter.value)
})

// 方法
const getSegmentClass = (status) => {
  return {
    'completed': status === 'completed',
    'processing': status === 'processing',
    'error': status === 'error',
    'waiting': status === 'waiting'
  }
}

const getSegmentIcon = (status) => {
  const icons = {
    'completed': Check,
    'processing': Loading,
    'error': Close,
    'waiting': Clock
  }
  return icons[status] || Clock
}

const getSegmentStatusText = (status) => {
  const texts = {
    'completed': '已完成',
    'processing': '处理中',
    'error': '处理失败',
    'waiting': '等待中'
  }
  return texts[status] || '未知'
}

const getLogIcon = (type) => {
  const icons = {
    'info': InfoFilled,
    'success': SuccessFilled,
    'warning': WarningFilled,
    'error': CircleCloseFilled
  }
  return icons[type] || InfoFilled
}

const formatTime = (seconds) => {
  if (seconds < 60) return `${seconds}秒`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}

const formatLogTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const retrySegment = (segmentIndex) => {
  emit('retry-segment', segmentIndex)
}

const skipSegment = (segmentIndex) => {
  emit('skip-segment', segmentIndex)
}

const clearLogs = () => {
  emit('clear-logs')
}
</script>

<style scoped>
.processing-monitor {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 进度卡片样式 */
.progress-card {
  border: 2px solid #e4e7ed;
  transition: all 0.3s;
}

.progress-card:hover {
  border-color: #409eff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  font-size: 32px;
  color: #409eff;
}

.header-icon.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.header-text h3 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 20px;
}

.header-text p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.overall-progress {
  margin: 20px 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-weight: 500;
  color: #606266;
}

.progress-value {
  color: #909399;
  font-size: 14px;
}

.progress-percent {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.main-progress {
  margin-bottom: 12px;
}

.current-step {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
  justify-content: center;
}

/* 统计网格样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.stat-item:hover {
  background: #ecf5ff;
  border-color: #b3d8ff;
}

.stat-icon {
  font-size: 24px;
  color: #409eff;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

/* 分段卡片样式 */
.segments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.segments-header h4 {
  margin: 0;
  color: #303133;
}

.segments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.segment-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s;
}

.segment-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.segment-card.completed {
  border-color: #67c23a;
  background: #f0f9ff;
}

.segment-card.processing {
  border-color: #409eff;
  background: #ecf5ff;
}

.segment-card.error {
  border-color: #f56c6c;
  background: #fef0f0;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.segment-title {
  font-weight: 500;
  color: #303133;
}

.segment-status-icon {
  font-size: 18px;
}

.segment-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.segment-info {
  display: flex;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.segment-status-text {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

/* 列表视图样式 */
.segments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.segment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
}

.segment-item:hover {
  background: #f8f9fa;
}

.segment-item.completed {
  border-color: #67c23a;
  background: #f0f9ff;
}

.segment-item.processing {
  border-color: #409eff;
  background: #ecf5ff;
}

.segment-item.error {
  border-color: #f56c6c;
  background: #fef0f0;
}

.segment-main {
  display: flex;
  align-items: center;
  gap: 12px;
}

.segment-details {
  display: flex;
  gap: 16px;
  margin-top: 4px;
}

.detail-item {
  font-size: 12px;
  color: #909399;
}

.segment-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 16px;
}

.status-icon.completed {
  color: #67c23a;
}

.status-icon.processing {
  color: #409eff;
}

.status-icon.error {
  color: #f56c6c;
}

.status-icon.waiting {
  color: #909399;
}

.status-text {
  font-size: 12px;
  color: #606266;
}

/* 日志样式 */
.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logs-header h4 {
  margin: 0;
  color: #303133;
}

.log-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f7fa;
  border-radius: 6px;
  padding: 12px;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 4px 0;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  transition: all 0.3s;
}

.log-item:hover {
  background: rgba(255, 255, 255, 0.5);
}

.log-time {
  color: #909399;
  min-width: 80px;
  font-family: monospace;
}

.log-icon {
  font-size: 14px;
}

.log-message {
  flex: 1;
  color: #606266;
}

.log-item.info .log-icon {
  color: #409eff;
}

.log-item.success .log-icon {
  color: #67c23a;
}

.log-item.warning .log-icon {
  color: #e6a23c;
}

.log-item.error .log-icon {
  color: #f56c6c;
}

/* 错误卡片样式 */
.error-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-icon {
  color: #f56c6c;
  font-size: 18px;
}

.error-header h4 {
  margin: 0;
  color: #f56c6c;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.error-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 6px;
}

.error-info {
  flex: 1;
}

.error-segment {
  font-weight: 500;
  color: #f56c6c;
  margin-right: 8px;
}

.error-message {
  color: #606266;
  font-size: 14px;
}

.error-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .processing-monitor {
    padding: 10px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .segments-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .logs-header,
  .segments-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .error-item {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
